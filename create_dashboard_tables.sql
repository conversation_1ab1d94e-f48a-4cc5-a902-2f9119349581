-- سكريپت إنشاء جداول لوحة التحكم العسكرية
-- Military Prison Dashboard Tables Creation Script

USE prison_db;
GO

-- جدول العنابر
CREATE TABLE cells (
    cell_id INT IDENTITY(1,1) PRIMARY KEY,
    cell_name NVARCHAR(50) NOT NULL,
    cell_type NVARCHAR(30) DEFAULT N'عادي', -- عادي، انفرادي، طبي
    capacity INT NOT NULL DEFAULT 10,
    current_occupancy INT DEFAULT 0,
    status NVARCHAR(20) DEFAULT N'available', -- available, occupied, maintenance
    building_number INT DEFAULT 1,
    floor_number INT DEFAULT 1,
    security_level NVARCHAR(20) DEFAULT N'متوسط', -- منخفض، متوسط، عالي
    created_date DATETIME DEFAULT GETDATE(),
    last_updated DATETIME DEFAULT GETDATE()
);

-- جدول النزلاء
CREATE TABLE prisoners (
    prisoner_id INT IDENTITY(1,1) PRIMARY KEY,
    prisoner_number NVARCHAR(20) UNIQUE NOT NULL,
    first_name NVARCHAR(50) NOT NULL,
    last_name NVARCHAR(50) NOT NULL,
    national_id NVARCHAR(20) UNIQUE,
    date_of_birth DATE,
    cell_id INT,
    admission_date DATETIME DEFAULT GETDATE(),
    expected_release_date DATE,
    status NVARCHAR(20) DEFAULT N'active', -- active, released, transferred
    crime_type NVARCHAR(100),
    sentence_duration_months INT,
    behavior_rating NVARCHAR(20) DEFAULT N'جيد', -- ممتاز، جيد، متوسط، سيء
    medical_notes NVARCHAR(500),
    created_date DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (cell_id) REFERENCES cells(cell_id)
);

-- جدول إحصائيات العنابر (للرسم البياني)
CREATE TABLE cells_statistics (
    stat_id INT IDENTITY(1,1) PRIMARY KEY,
    cell_name NVARCHAR(50) NOT NULL,
    prisoner_count INT DEFAULT 0,
    capacity INT DEFAULT 0,
    occupancy_percentage DECIMAL(5,2) DEFAULT 0,
    last_updated DATETIME DEFAULT GETDATE()
);

-- جدول الزيارات
CREATE TABLE visits (
    visit_id INT IDENTITY(1,1) PRIMARY KEY,
    prisoner_id INT NOT NULL,
    visitor_name NVARCHAR(100) NOT NULL,
    visitor_relation NVARCHAR(50),
    visitor_national_id NVARCHAR(20),
    visit_date DATETIME DEFAULT GETDATE(),
    visit_duration_minutes INT DEFAULT 30,
    status NVARCHAR(20) DEFAULT N'scheduled', -- scheduled, completed, cancelled
    notes NVARCHAR(500),
    approved_by NVARCHAR(50),
    FOREIGN KEY (prisoner_id) REFERENCES prisoners(prisoner_id)
);

-- جدول الموظفين
CREATE TABLE staff (
    staff_id INT IDENTITY(1,1) PRIMARY KEY,
    staff_number NVARCHAR(20) UNIQUE NOT NULL,
    first_name NVARCHAR(50) NOT NULL,
    last_name NVARCHAR(50) NOT NULL,
    position NVARCHAR(50) NOT NULL,
    department NVARCHAR(50),
    shift NVARCHAR(20), -- صباحي، مسائي، ليلي
    phone NVARCHAR(20),
    email NVARCHAR(100),
    hire_date DATE DEFAULT GETDATE(),
    status NVARCHAR(20) DEFAULT N'active', -- active, inactive, vacation
    security_clearance NVARCHAR(20) DEFAULT N'متوسط'
);

-- جدول التنبيهات
CREATE TABLE alerts (
    alert_id INT IDENTITY(1,1) PRIMARY KEY,
    alert_type NVARCHAR(50) NOT NULL, -- أمني، طبي، صيانة، طوارئ
    title NVARCHAR(100) NOT NULL,
    description NVARCHAR(500),
    priority NVARCHAR(20) DEFAULT N'متوسط', -- منخفض، متوسط، عالي، طوارئ
    status NVARCHAR(20) DEFAULT N'active', -- active, resolved, dismissed
    related_prisoner_id INT NULL,
    related_cell_id INT NULL,
    created_by NVARCHAR(50),
    created_date DATETIME DEFAULT GETDATE(),
    resolved_date DATETIME NULL,
    resolved_by NVARCHAR(50) NULL,
    FOREIGN KEY (related_prisoner_id) REFERENCES prisoners(prisoner_id),
    FOREIGN KEY (related_cell_id) REFERENCES cells(cell_id)
);

-- إدراج بيانات تجريبية للعنابر
INSERT INTO cells (cell_name, cell_type, capacity, current_occupancy, status, building_number, floor_number, security_level) VALUES
(N'العنبر الأول', N'عادي', 30, 25, N'occupied', 1, 1, N'متوسط'),
(N'العنبر الثاني', N'عادي', 25, 18, N'occupied', 1, 1, N'متوسط'),
(N'العنبر الثالث', N'عادي', 35, 32, N'occupied', 1, 2, N'متوسط'),
(N'العنبر الرابع', N'انفرادي', 20, 15, N'occupied', 2, 1, N'عالي'),
(N'العنبر الخامس', N'عادي', 30, 28, N'occupied', 2, 1, N'متوسط'),
(N'العنبر السادس', N'طبي', 15, 8, N'available', 2, 2, N'منخفض'),
(N'العنبر السابع', N'عادي', 25, 0, N'maintenance', 3, 1, N'متوسط'),
(N'العنبر الثامن', N'عادي', 30, 22, N'occupied', 3, 1, N'متوسط');

-- إدراج بيانات تجريبية لإحصائيات العنابر
INSERT INTO cells_statistics (cell_name, prisoner_count, capacity, occupancy_percentage) VALUES
(N'العنبر الأول', 25, 30, 83.33),
(N'العنبر الثاني', 18, 25, 72.00),
(N'العنبر الثالث', 32, 35, 91.43),
(N'العنبر الرابع', 15, 20, 75.00),
(N'العنبر الخامس', 28, 30, 93.33),
(N'العنبر السادس', 8, 15, 53.33),
(N'العنبر السابع', 0, 25, 0.00),
(N'العنبر الثامن', 22, 30, 73.33);

-- إدراج بيانات تجريبية للنزلاء
INSERT INTO prisoners (prisoner_number, first_name, last_name, national_id, date_of_birth, cell_id, expected_release_date, crime_type, sentence_duration_months, behavior_rating) VALUES
(N'P001', N'أحمد', N'محمد', N'1234567890', '1990-05-15', 1, '2024-12-31', N'مخالفة عسكرية', 12, N'جيد'),
(N'P002', N'محمد', N'علي', N'1234567891', '1988-03-20', 1, '2024-08-15', N'غياب بدون إذن', 6, N'ممتاز'),
(N'P003', N'خالد', N'أحمد', N'1234567892', '1992-07-10', 2, '2025-01-20', N'عصيان أوامر', 18, N'متوسط'),
(N'P004', N'عبدالله', N'سالم', N'1234567893', '1985-12-05', 3, '2024-09-30', N'إهمال في الواجب', 8, N'جيد'),
(N'P005', N'سالم', N'محمد', N'1234567894', '1991-09-25', 4, '2025-06-15', N'مخالفة أمنية', 24, N'سيء');

-- إدراج بيانات تجريبية للموظفين
INSERT INTO staff (staff_number, first_name, last_name, position, department, shift, phone, security_clearance) VALUES
(N'S001', N'العقيد محمد', N'الأحمد', N'مدير السجن', N'الإدارة', N'صباحي', N'123456789', N'عالي'),
(N'S002', N'الرائد أحمد', N'السالم', N'نائب المدير', N'الإدارة', N'صباحي', N'123456790', N'عالي'),
(N'S003', N'النقيب خالد', N'المحمد', N'رئيس الأمن', N'الأمن', N'صباحي', N'123456791', N'عالي'),
(N'S004', N'الملازم سالم', N'العلي', N'ضابط أمن', N'الأمن', N'مسائي', N'123456792', N'متوسط'),
(N'S005', N'الدكتور عبدالله', N'الطبيب', N'طبيب السجن', N'الطبي', N'صباحي', N'123456793', N'متوسط');

-- إدراج بيانات تجريبية للتنبيهات
INSERT INTO alerts (alert_type, title, description, priority, related_prisoner_id, related_cell_id, created_by) VALUES
(N'أمني', N'مشاجرة في العنبر الثالث', N'حدثت مشاجرة بين نزيلين في العنبر الثالث', N'عالي', NULL, 3, N'ضابط الأمن'),
(N'طبي', N'حالة طبية طارئة', N'نزيل يحتاج رعاية طبية فورية', N'طوارئ', 5, 4, N'الطبيب المناوب'),
(N'صيانة', N'عطل في نظام التكييف', N'العنبر السابع يحتاج صيانة للتكييف', N'متوسط', NULL, 7, N'فني الصيانة');

-- إنشاء فيو لإحصائيات سريعة
CREATE VIEW dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM prisoners WHERE status = 'active') as total_prisoners,
    (SELECT COUNT(*) FROM cells WHERE status = 'available') as available_cells,
    (SELECT COUNT(*) FROM cells WHERE status = 'occupied') as occupied_cells,
    (SELECT COUNT(*) FROM alerts WHERE status = 'active') as active_alerts,
    (SELECT COUNT(*) FROM visits WHERE visit_date >= CAST(GETDATE() AS DATE)) as today_visits,
    (SELECT COUNT(*) FROM staff WHERE status = 'active') as active_staff;

-- إنشاء إجراء مخزن لتحديث إحصائيات العنابر
CREATE PROCEDURE UpdateCellsStatistics
AS
BEGIN
    UPDATE cells_statistics 
    SET 
        prisoner_count = (
            SELECT COUNT(*) 
            FROM prisoners p 
            INNER JOIN cells c ON p.cell_id = c.cell_id 
            WHERE c.cell_name = cells_statistics.cell_name 
            AND p.status = 'active'
        ),
        capacity = (
            SELECT capacity 
            FROM cells c 
            WHERE c.cell_name = cells_statistics.cell_name
        ),
        occupancy_percentage = (
            CASE 
                WHEN (SELECT capacity FROM cells c WHERE c.cell_name = cells_statistics.cell_name) > 0
                THEN (
                    SELECT COUNT(*) * 100.0 / c.capacity
                    FROM prisoners p 
                    INNER JOIN cells c ON p.cell_id = c.cell_id 
                    WHERE c.cell_name = cells_statistics.cell_name 
                    AND p.status = 'active'
                )
                ELSE 0
            END
        ),
        last_updated = GETDATE();
END;

-- تشغيل الإجراء لتحديث الإحصائيات
EXEC UpdateCellsStatistics;

PRINT N'تم إنشاء جداول لوحة التحكم العسكرية بنجاح';
PRINT N'الجداول المنشأة:';
PRINT N'- cells (العنابر)';
PRINT N'- prisoners (النزلاء)';
PRINT N'- cells_statistics (إحصائيات العنابر)';
PRINT N'- visits (الزيارات)';
PRINT N'- staff (الموظفين)';
PRINT N'- alerts (التنبيهات)';
PRINT N'- dashboard_stats (فيو الإحصائيات)';
PRINT N'- UpdateCellsStatistics (إجراء تحديث الإحصائيات)';
