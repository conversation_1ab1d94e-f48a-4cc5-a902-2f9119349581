unit ufrmMain;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls,
  Vcl.ExtCtrls, Data.Win.ADODB, System.Win.Registry, Vcl.Menus, Data.DB,
  frxClass, frxDBSet, Vcl.Grids, Vcl.DBGrids, Vcl.Imaging.jpeg, dxNavBar,
  Vcl.ImgList, Vcl.ExtDlgs,FileCtrl, System.ImageList, cxGraphics, cxControls,
  cxLookAndFeels, cxLookAndFeelPainters, dxSkinsCore, dxSkinBasic,
  dxSkinGlassOceans, dxSkiniMaginary, dxSkinOffice2019Black,
  dxSkinOffice2019Colorful, dxSkinOffice2019DarkGray, dxSkinOffice2019White,
  dxSkinXmas2008Blue, dxNavBarCollns, cxClasses, dxNavBarBase, dxGDIPlusClasses;

type
  TfrmMain = class(TForm)
    Panel1: TPanel;
    Label2: TLabel;
    Label3: TLabel;
    ComboBox1: TComboBox;
    Edit1: TEdit;
    MainMenu1: TMainMenu;
    xxx1: TMenuItem;
    N29: TMenuItem;
    N5: TMenuItem;
    newart1: TMenuItem;
    N43: TMenuItem;
    N79: TMenuItem;
    N21: TMenuItem;
    N30: TMenuItem;
    N39: TMenuItem;
    N41: TMenuItem;
    N2: TMenuItem;
    N40: TMenuItem;
    N44: TMenuItem;
    N75: TMenuItem;
    N19: TMenuItem;
    cash1: TMenuItem;
    N3: TMenuItem;
    N10: TMenuItem;
    N11: TMenuItem;
    N12: TMenuItem;
    N50: TMenuItem;
    N35: TMenuItem;
    N16: TMenuItem;
    N46: TMenuItem;
    N49: TMenuItem;
    N57: TMenuItem;
    N23: TMenuItem;
    N28: TMenuItem;
    N9: TMenuItem;
    N52: TMenuItem;
    N33: TMenuItem;
    N66: TMenuItem;
    N14: TMenuItem;
    N24: TMenuItem;
    N25: TMenuItem;
    qinfo: TADOQuery;
    frxDBDataset1: TfrxDBDataset;
    qbranch: TADOQuery;
    frxDBDataset2: TfrxDBDataset;
    qpermissions: TADOQuery;
    qsafenames: TADOQuery;
    qstorenames: TADOQuery;
    N1: TMenuItem;
    ADOConnection1: TADOConnection;
    N4: TMenuItem;
    xxx2: TMenuItem;
    qgen: TADOQuery;
    N6: TMenuItem;
    Image2: TImage;
    OpenDialog1: TOpenDialog;
    OpenPictureDialog1: TOpenPictureDialog;
    ImageList1: TImageList;
    dxNavBar1: TdxNavBar;
    dxNavBar1Group2: TdxNavBarGroup;
    dxNavBar1Group3: TdxNavBarGroup;
    dxNavBar1Group1: TdxNavBarGroup;
    dxNavBar1Item1: TdxNavBarItem;
    dxNavBar1Item2: TdxNavBarItem;
    dxNavBar1Item3: TdxNavBarItem;
    dxNavBar1Item4: TdxNavBarItem;
    dxNavBar1Item5: TdxNavBarItem;
    dxNavBar1Item6: TdxNavBarItem;
    dxNavBar1Item7: TdxNavBarItem;
    dxNavBar1Item8: TdxNavBarItem;
    dxNavBar1Item9: TdxNavBarItem;
    dxNavBar1Item10: TdxNavBarItem;
    dxNavBar1Item11: TdxNavBarItem;
    dxNavBar1Item12: TdxNavBarItem;
    dxNavBar1Item13: TdxNavBarItem;
    dxNavBar1Item14: TdxNavBarItem;
    dxNavBar1Item15: TdxNavBarItem;
    dxNavBar1Item16: TdxNavBarItem;
    dxNavBar1Item17: TdxNavBarItem;
    dxNavBar1Item18: TdxNavBarItem;
    dxNavBar1Item19: TdxNavBarItem;
    dxNavBar1Item20: TdxNavBarItem;
    dxNavBar1Item21: TdxNavBarItem;
    dxNavBar1Item22: TdxNavBarItem;
    dxNavBar1Item23: TdxNavBarItem;
    dxNavBar1Item24: TdxNavBarItem;
    dxNavBar1Item25: TdxNavBarItem;
    dxNavBar1Item26: TdxNavBarItem;
    Panel2: TPanel;
    Label1: TLabel;
    dxNavBar1Item27: TdxNavBarItem;
    ADOConnection2: TADOConnection;
    qbackup: TADOQuery;
    Label4: TLabel;
    Label5: TLabel;
    Label6: TLabel;
    Panel3: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure ComboBox1Enter(Sender: TObject);
    procedure ComboBox1KeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure ComboBox1KeyPress(Sender: TObject; var Key: Char);
    procedure Edit1KeyPress(Sender: TObject; var Key: Char);
    procedure N79Click(Sender: TObject);
    procedure N4Click(Sender: TObject);
    procedure xxx2Click(Sender: TObject);
    procedure N57Click(Sender: TObject);
    procedure N2Click(Sender: TObject);
    procedure N29Click(Sender: TObject);
    procedure N6Click(Sender: TObject);
    procedure Button1Click(Sender: TObject);
    procedure Button3Click(Sender: TObject);
    procedure dxNavBar1Item15Click(Sender: TObject);
    procedure dxNavBar1Item11Click(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure CloseAllForms;
    procedure dxNavBar1Item10Click(Sender: TObject);
    procedure dxNavBar1Item9Click(Sender: TObject);
    procedure dxNavBar1Item24Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;
  via_internet: Boolean;
  myservername, orgservername, server_idname, orgidname, server_password,
    orgpassword, mydatabasename, orgdatabasename, theconnectionstring,
    themainconnectionstring: string;
  cangivepermissions, canprint, canaltercash, canalterfin, canregfin,
    canpaycash, canreadfin, canread, cansell, canrecievecash, canaltersells,
    canreadcash, canalter: Boolean;
  theusername, theuserid: string;
  mainaddress, subaddress, telephone, accuracy, pagetailline1, pagetailline2,
    maindatabasename: string;
  defaultsafeid, defaultstoreid, subtract_hours, tryopenconnectionNo: Integer;
  check_tax: Real;
  theperiodid: Integer;
  theperiodname, theperiodstate: string;
  theperiodstart, theperiodend: TDate;
  mainservername: string;
  mainserverexist: Boolean;
  theglobalarticlename, theglobalarticlecode, theglobalcustname,
    theglobalstorename, theglobalmainsort, theglobalsubsort,
    theglobalsearchtext: string;
  theglobalarticleprice, theglobalartcost: Real;
  theglobalarticleid, theglobalcustaccid, theglobalstoreid: Integer;
  NextFormType, NextFormMode: string;
  tradformcount: Integer;
  branchno, branchid: Integer;
  branchname, branchcityname, app_level: string;
  branchcityid: Integer;
  defaultcoin: string;
  addphotono:string;
  gencompanyname:string;
implementation

uses
  ArabicCaptionUnit, sharedfunctions, ubetweenstores, Ufrm_articles, newempunit, Usrchemp, Uaddblace, Ualteremp, Unit1;

{$R *.dfm}

procedure TfrmMain.CloseAllForms;
var
  i: Integer;
begin
  for i := 0 to Application.ComponentCount - 1 do
  begin
    if Application.Components[i] is TForm then
    begin
      if Application.Components[i] <> Self then
        TForm(Application.Components[i]).Close;
    end;
  end;
end;
procedure TfrmMain.Button1Click(Sender: TObject);
begin
  newemp := Tnewemp.Create(Application);

 newemp.ShowModal;

end;

procedure TfrmMain.Button3Click(Sender: TObject);
begin
     // if Assigned(srchemp) then Exit;
  srchemp := Tsrchemp.Create(Application);

  srchemp.ShowModal;

end;

procedure TfrmMain.ComboBox1Enter(Sender: TObject);
var
  q: TADOQuery;
  conn: TADOConnection;
begin
//if ComboBox1.Items.Count>0 then Exit ;
//
//    ADOConnection1.ConnectionString := 'FILE NAME=' +
//      ExtractFilePath(Application.ExeName) + '\prison.udl';
//   ADOConnection1.Open();
//   theconnectionstring:= ADOConnection1.ConnectionString;
//
//  ComboBox1.Items.Clear;
//  try
//
//    conn := TADOConnection.Create(nil);
//    conn.LoginPrompt := False;
//    conn.ConnectionString := theconnectionstring;
//    try
//      conn.Open();
//    except
//
//      if tryopenconnectionNo > 1 then
//        Application.Terminate;
//      tryopenconnectionNo := tryopenconnectionNo + 1;
//    end;
//    qinfo.Connection := conn;
//    qbranch.Connection := conn;
//    qpermissions.Connection := conn;
//    qsafenames.Connection := conn;
//    qstorenames.Connection := conn;
//    qpermissions.Connection := conn;
//    try
//      qpermissions.SQL.Text := 'select * from permissions';
//      qpermissions.Open;
//      if not qpermissions.IsEmpty then
//      begin
//        while not qpermissions.Eof do
//        begin
//          ComboBox1.Items.Add(qpermissions['theusername']);
//          qpermissions.Next;
//        end;
//      end;
//
//      qinfo.SQL.Text := 'select * from generaltable ';
//      qinfo.Open;
//      gencompanyname:=qinfo['gencompanyname'];
//      qstorenames.SQL.Clear;
//      qstorenames.SQL.Add('select * from storenames ');
//      qstorenames.SQL.Add(' order by storename');
//      qstorenames.Open;
//
//    except
//      on E: exception do
//        ShowMessage(E.Message);
//    end;
//  finally
//    qbranch.Connection := nil;
//    qinfo.Connection := nil;
//    qpermissions.Connection := nil;
//    qsafenames.Connection := nil;
//    qstorenames.Connection := nil;
//    qpermissions.Connection := nil;
//    conn.Close;
//    conn.Free;
//
//  end;
end;

procedure TfrmMain.ComboBox1KeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  if (Key = VK_RETURN) then
  begin
    if ComboBox1.Text <> '' then
      Edit1.SetFocus
    else
      ShowMessage('��� ��������');
  end;
end;

procedure TfrmMain.ComboBox1KeyPress(Sender: TObject; var Key: Char);
begin
  Key := #0;
end;

procedure TfrmMain.dxNavBar1Item10Click(Sender: TObject);
var
  I: Integer;
begin
  for I := Screen.FormCount - 1 downto 0 do
  begin
  if Screen.Forms[I].Name<>'frmMain' then  Screen.Forms[I].Close;
  end;


  alteremp := Talteremp.Create(Self);
  alteremp.Parent := Panel2;
  //newemp.Align := alClient;
  alteremp.BorderIcons := [];
  alteremp.BorderStyle := bsNone;
  alteremp.Top:=20;
  alteremp.Left:=20;
  alteremp.Show;
end;

procedure TfrmMain.dxNavBar1Item11Click(Sender: TObject);
var
  I: Integer;
begin
  for I := Screen.FormCount - 1 downto 0 do
  begin
  if Screen.Forms[I].Name<>'frmMain' then  Screen.Forms[I].Close;
  end;

  newemp := Tnewemp.Create(Self);
  newemp.Parent := Panel2;
  //newemp.Align := alClient;
  newemp.BorderIcons := [];
  newemp.BorderStyle := bsNone;
  newemp.Top:=20;
  newemp.Left:=20;
  newemp.Show;

end;

procedure TfrmMain.dxNavBar1Item15Click(Sender: TObject);
var
  I: Integer;
begin
  for I := Screen.FormCount - 1 downto 0 do
  begin
  if Screen.Forms[I].Name<>'frmMain' then  Screen.Forms[I].Close;
  end;
  srchemp := Tsrchemp.Create(Self);
  //srchemp.Parent := Panel2;
  //newemp.Align := alClient;
  //srchemp.BorderIcons := [];
  //srchemp.BorderStyle := bsNone;
  srchemp.Top:=20;
  srchemp.Left:=20;
  srchemp.Showmodal;

end;

procedure TfrmMain.dxNavBar1Item24Click(Sender: TObject);
begin
   Form1 := TForm1.Create(Self);
  Form1.Showmodal;
end;

procedure TfrmMain.dxNavBar1Item9Click(Sender: TObject);
var
  chosenDirectory, backupfilename: string;
begin
  if ADOConnection2.Connected = True then
    ADOConnection2.Close;
  ADOConnection2.ConnectionString :=
    'Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security' +
    ' Info=False;Initial Catalog=master;Data Source=' + myservername +
    ';Use Procedure for Prepare=1;Auto Translate=True;Packet Size=4096;Workstation ID=VAIO;Use Encryption for Data=False;Tag with column collation when possible=False';
  ADOConnection2.Open();

  if SelectDirectory('Select a directory', '', chosenDirectory) then
  begin
    try
      ADOConnection2.BeginTrans;
      backupfilename := mydatabasename + formatdatetime('HHMMDDMMYYYY',
        now) + '.bak';
      qbackup.SQL.Clear;
      qbackup.SQL.Add('BACKUP DATABASE [' + mydatabasename + ']' + 'TO DISK = N'
        + QuotedStr(chosenDirectory + '\' + backupfilename));
      qbackup.ExecSQL;
      ADOConnection2.CommitTrans;
      ShowMessage('�� ��� ���� �� ��������');
    except

      on E: Exception do
      begin
        ADOConnection2.RollbackTrans;
        ShowMessage('��� �� ��� ����� ��������');
        ShowMessage(E.Message);
      end;
    end;
  end;


end;

procedure TfrmMain.Edit1KeyPress(Sender: TObject; var Key: Char);
var
  q: TADOQuery;
  conn: TADOConnection;
begin
  if Key = Char(VK_RETURN) then
  begin
    Key := #0;
    begin
      checkinternet;
      try
        try
          conn := TADOConnection.Create(nil);
          conn.LoginPrompt := False;
          q := TADOQuery.Create(nil);
          conn.ConnectionString := theconnectionstring;
          conn.Open();
          q.Connection := conn;
          q.SQL.Text := 'select * from permissions where  theusername=' + '''' +
            Trim(ComboBox1.Text) + '''' + 'and thepassword=' + '''' +
            Trim(Edit1.Text) + '''';
          q.Open;
          if q.IsEmpty then
          begin
            ShowMessage('���� ������ ��� ������');
            Edit1.Text := '';
          end
          else
          begin
          //Self.Menu := MainMenu1;
            theusername := Trim(q['theusername']);
            theuserid := q['perid'];
            Panel1.Visible := False;
            cangivepermissions := q['cangivepermissions'];
            canprint := q['canprint'];
            canaltercash := q['canaltercash'];
            canalterfin := q['canalterfin'];
            canregfin := q['canregfin'];
            canpaycash := q['canpaycash'];
            canreadfin := q['canreadfin'];
            canread := q['canread'];
            cansell := q['cansell'];
            canrecievecash := q['canrecievecash'];
            canaltersells := q['canaltersells'];
            canreadcash := q['canreadcash'];

            WindowState := wsMaximized;
           // Constraints.MinHeight := 600;
           // Constraints.MinWidth := 1000;

            q.SQL.Text := 'select * from generaltable';

//            q.Open;
//            if not q.IsEmpty then
//            begin
//              // companyname := trim(q['frmMain.qinfo['gencompanyname']']);
//              // subcompanyname := q['gensubcompanyname'];
//              mainaddress := q['genmainaddress'];
//              subaddress := q['gensubaddress'];
//              telephone := q['gentelephone'];
//              accuracy := Trim('�����');
//              pagetailline1 := q['pagetailline1'];
//              pagetailline2 := q['pagetailline2'];;
//              check_tax := q['check_tax'];
//              subtract_hours := q['subtract_hours'];
//
//              q.SQL.Text := 'select * from periods where  periodnotes=' +
//                QuotedStr(Trim('opened'));
//              q.Open;
//              if not q.IsEmpty then
//              begin
//                q.Last;
//                theperiodid := q['periodid'];
//                theperiodname := Trim(q['periodname']);
//                theperiodstart := q['periodstartdate'];
//                theperiodend := q['periodenddate'];
//                theperiodstate := q['periodnotes'];
//              end
//              else
//              begin
//                ShowMessage('��� ��� �� ��� ����� ������');
//                q.Free;
//                conn.Free;
//              end;
//
//            end
//            else
//            begin
//              ShowMessage('��� ��� �� ��� ��� ������ ������ �����');
//              q.Free;
//              conn.Free;
//            end;
            dxNavBar1.Visible:=True;
          end;
        except
          on E: exception do
            ShowMessage(E.Message);
        end;
      finally
        q.Free;
        conn.Free;
      end;
    end;

  end;
end;

procedure TfrmMain.FormClose(Sender: TObject; var Action: TCloseAction);
begin
try
    newemp.Free;
    newemp := nil; // Optionally, set the form variable to nil after freeing it
except

end;
end;

procedure TfrmMain.FormCreate(Sender: TObject);
var
  q: TADOQuery;
  conn: TADOConnection;
begin
if ComboBox1.Items.Count>0 then Exit ;

    ADOConnection1.ConnectionString := 'FILE NAME=' +
      ExtractFilePath(Application.ExeName) + '\prison.udl';
   ADOConnection1.Open();
   theconnectionstring:= ADOConnection1.ConnectionString;

  ComboBox1.Items.Clear;
  try

    conn := TADOConnection.Create(nil);
    conn.LoginPrompt := False;
    conn.ConnectionString := theconnectionstring;
    try
      conn.Open();
    except

      if tryopenconnectionNo > 1 then
        Application.Terminate;
      tryopenconnectionNo := tryopenconnectionNo + 1;
    end;
    qinfo.Connection := conn;
    qbranch.Connection := conn;
    qpermissions.Connection := conn;
    qsafenames.Connection := conn;
    qstorenames.Connection := conn;
    qpermissions.Connection := conn;
    try
      qpermissions.SQL.Text := 'select * from permissions';
      qpermissions.Open;
      if not qpermissions.IsEmpty then
      begin
        while not qpermissions.Eof do
        begin
          ComboBox1.Items.Add(qpermissions['theusername']);
          qpermissions.Next;
        end;
      end;

      qinfo.SQL.Text := 'select * from generaltable ';
      qinfo.Open;
      gencompanyname:=qinfo['gencompanyname'];
      qstorenames.SQL.Clear;
      qstorenames.SQL.Add('select * from storenames ');
      qstorenames.SQL.Add(' order by storename');
      qstorenames.Open;

    except
      on E: exception do
        ShowMessage(E.Message);
    end;
  finally
    qbranch.Connection := nil;
    qinfo.Connection := nil;
    qpermissions.Connection := nil;
    qsafenames.Connection := nil;
    qstorenames.Connection := nil;
    qpermissions.Connection := nil;
    conn.Close;
    conn.Free;

  end;

ActiveControl:=Edit1;
Self.Menu := nil;
  via_internet := False;
  Label1.Caption:='������ '+gencompanyname;

  // إخفاء عناصر الواجهة حتى يتم تسجيل الدخول
  Panel1.Visible := True;  // إظهار panel تسجيل الدخول
  dxNavBar1.Visible := False;  // إخفاء شريط التنقل
  Self.Hide;  // إخفاء الفورم الرئيسي في البداية
//  checkinternetmain;
end;

procedure TfrmMain.N29Click(Sender: TObject);
begin
   addblace := Taddblace.Create(Application);

  addblace.Show;

end;

procedure TfrmMain.N2Click(Sender: TObject);
begin
      if Assigned(srchemp) then Exit;
  srchemp := Tsrchemp.Create(Application);

  srchemp.Show;

end;

procedure TfrmMain.N4Click(Sender: TObject);
begin
  newemp := Tnewemp.Create(Application);

  newemp.Show;

end;

procedure TfrmMain.N57Click(Sender: TObject);
var
  theagentname: string;
  am: Real;
begin

  if MessageDlg(' ���� ����� ���� ' + #13 + ' �� ���� ���������',
    mtConfirmation, [mbYes, mbNo], 0) = mrno then
    Abort
  else
  begin
    theagentname := InputBox('����� ����', '������', '');
    if theagentname = '' then
    begin
      ShowMessage('��� ������');
      Exit;
    end;
  end;

  frmmain.adoconnection1.BeginTrans;
  try
    qgen.SQL.Clear;
    qgen.SQL.Add('select * from storenames where storename= ' +
      QuotedStr(theagentname));
    qgen.Open;
    if qgen.IsEmpty then
    begin
      qgen.SQL.Clear;
      qgen.SQL.Add
        ('insert into storenames (storename,storestate) values(' +
        QuotedStr(theagentname) + ',' + QuotedStr('1')+ ')');
      qgen.ExecSQL;

    end
    else
    begin
      ShowMessage('��� ������ �����');
      Abort;
    end;

    frmmain.adoconnection1.CommitTrans;

  except
    on E: Exception do
    begin
      frmmain.adoconnection1.RollbackTrans;
      ShowMessage('��� �� ��� �������');
      ShowMessage(E.Message);
    end;

  end;


end;

procedure TfrmMain.N6Click(Sender: TObject);
begin
alteremp := Talteremp.Create(Application);
alteremp.show;
end;

procedure TfrmMain.N79Click(Sender: TObject);
begin
  betweenstores := Tbetweenstores.Create(Application);

  betweenstores.Show;

end;

procedure TfrmMain.xxx2Click(Sender: TObject);
begin
  srchemp := Tsrchemp.Create(Application);

  srchemp.Show;

end;

end.
