object Form4: TForm4
  Left = 0
  Top = 0
  Caption = #1604#1608#1581#1577' '#1575#1604#1578#1581#1603#1605' - '#1606#1592#1575#1605' '#1575#1604#1581#1576#1587' '#1575#1604#1593#1587#1603#1585#1610
  ClientHeight = 768
  ClientWidth = 1366
  Color = 2829099
  Font.Charset = ANSI_CHARSET
  Font.Color = clWhite
  Font.Height = -13
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  WindowState = wsMaximized
  OnCreate = FormCreate
  OnResize = FormResize
  OnShow = FormShow
  TextHeight = 17
  object pnlHeader: TPanel
    Left = 0
    Top = 0
    Width = 1366
    Height = 80
    Align = alTop
    BevelOuter = bvNone
    Color = 1644825
    ParentBackground = False
    TabOrder = 0
    object lblTitle: TLabel
      Left = 100
      Top = 20
      Width = 400
      Height = 25
      Caption = #1606#1592#1575#1605' '#1573#1583#1575#1585#1577' '#1575#1604#1581#1576#1587' '#1575#1604#1593#1587#1603#1585#1610' - '#1604#1608#1581#1577' '#1575#1604#1578#1581#1603#1605
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -19
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object lblDate: TLabel
      Left = 1100
      Top = 15
      Width = 120
      Height = 20
      Alignment = taRightJustify
      Caption = '2024/01/01'
      Font.Charset = ANSI_CHARSET
      Font.Color = clSilver
      Font.Height = -15
      Font.Name = 'Segoe UI'
      Font.Style = []
      ParentFont = False
    end
    object lblTime: TLabel
      Left = 1100
      Top = 45
      Width = 120
      Height = 20
      Alignment = taRightJustify
      Caption = '12:00:00'
      Font.Charset = ANSI_CHARSET
      Font.Color = clSilver
      Font.Height = -15
      Font.Name = 'Segoe UI'
      Font.Style = []
      ParentFont = False
    end
    object imgLogo: TImage
      Left = 20
      Top = 10
      Width = 60
      Height = 60
      Stretch = True
    end
  end
  object pnlRight: TPanel
    Left = 1116
    Top = 80
    Width = 250
    Height = 688
    Align = alRight
    BevelOuter = bvNone
    Color = 2303797
    ParentBackground = False
    TabOrder = 1
    object btnPrisoners: TButton
      Left = 20
      Top = 20
      Width = 210
      Height = 50
      Caption = #1573#1583#1575#1585#1577' '#1575#1604#1606#1586#1604#1575#1569
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 0
      OnClick = btnPrisonersClick
    end
    object btnCells: TButton
      Left = 20
      Top = 80
      Width = 210
      Height = 50
      Caption = #1573#1583#1575#1585#1577' '#1575#1604#1593#1606#1575#1576#1585
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 1
      OnClick = btnCellsClick
    end
    object btnReports: TButton
      Left = 20
      Top = 140
      Width = 210
      Height = 50
      Caption = #1575#1604#1578#1602#1575#1585#1610#1585
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 2
      OnClick = btnReportsClick
    end
    object btnSecurity: TButton
      Left = 20
      Top = 200
      Width = 210
      Height = 50
      Caption = #1575#1604#1571#1605#1606' '#1608#1575#1604#1581#1585#1575#1587#1577
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 3
      OnClick = btnSecurityClick
    end
    object btnVisitors: TButton
      Left = 20
      Top = 260
      Width = 210
      Height = 50
      Caption = #1573#1583#1575#1585#1577' '#1575#1604#1586#1610#1575#1585#1575#1578
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 4
      OnClick = btnVisitorsClick
    end
    object btnMedical: TButton
      Left = 20
      Top = 320
      Width = 210
      Height = 50
      Caption = #1575#1604#1582#1583#1605#1575#1578' '#1575#1604#1591#1576#1610#1577
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 5
      OnClick = btnMedicalClick
    end
    object btnFood: TButton
      Left = 20
      Top = 380
      Width = 210
      Height = 50
      Caption = #1573#1583#1575#1585#1577' '#1575#1604#1591#1593#1575#1605
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 6
      OnClick = btnFoodClick
    end
    object btnMaintenance: TButton
      Left = 20
      Top = 440
      Width = 210
      Height = 50
      Caption = #1575#1604#1589#1610#1575#1606#1577' '#1608#1575#1604#1578#1585#1605#1610#1605
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 7
      OnClick = btnMaintenanceClick
    end
    object btnStaff: TButton
      Left = 20
      Top = 500
      Width = 210
      Height = 50
      Caption = #1573#1583#1575#1585#1577' '#1575#1604#1605#1608#1592#1601#1610#1606
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 8
      OnClick = btnStaffClick
    end
    object btnSettings: TButton
      Left = 20
      Top = 560
      Width = 210
      Height = 50
      Caption = #1575#1604#1573#1593#1583#1575#1583#1575#1578
      Font.Charset = ANSI_CHARSET
      Font.Color = clWhite
      Font.Height = -13
      Font.Name = 'Segoe UI'
      Font.Style = [fsBold]
      ParentFont = False
      TabOrder = 9
      OnClick = btnSettingsClick
    end
  end
  object pnlMain: TPanel
    Left = 0
    Top = 80
    Width = 1116
    Height = 688
    Align = alClient
    BevelOuter = bvNone
    Color = 3289650
    ParentBackground = False
    TabOrder = 2
    object pnlInfo: TPanel
      Left = 0
      Top = 0
      Width = 1116
      Height = 120
      Align = alTop
      BevelOuter = bvNone
      Color = 3289650
      ParentBackground = False
      TabOrder = 0
      object pnlTotalPrisoners: TPanel
        Left = 20
        Top = 20
        Width = 250
        Height = 80
        BevelOuter = bvRaised
        Color = 4342338
        ParentBackground = False
        TabOrder = 0
        object lblTotalPrisoners: TLabel
          Left = 20
          Top = 15
          Width = 100
          Height = 17
          Caption = #1605#1580#1605#1608#1593' '#1575#1604#1606#1586#1604#1575#1569
          Font.Charset = ANSI_CHARSET
          Font.Color = clSilver
          Font.Height = -13
          Font.Name = 'Segoe UI'
          Font.Style = []
          ParentFont = False
        end
        object lblTotalPrisonersValue: TLabel
          Left = 20
          Top = 40
          Width = 30
          Height = 25
          Caption = '118'
          Font.Charset = ANSI_CHARSET
          Font.Color = clWhite
          Font.Height = -19
          Font.Name = 'Segoe UI'
          Font.Style = [fsBold]
          ParentFont = False
        end
      end
      object pnlAvailableCells: TPanel
        Left = 290
        Top = 20
        Width = 250
        Height = 80
        BevelOuter = bvRaised
        Color = 4342338
        ParentBackground = False
        TabOrder = 1
        object lblAvailableCells: TLabel
          Left = 20
          Top = 15
          Width = 100
          Height = 17
          Caption = #1575#1604#1593#1606#1575#1576#1585' '#1575#1604#1605#1578#1575#1581#1577
          Font.Charset = ANSI_CHARSET
          Font.Color = clSilver
          Font.Height = -13
          Font.Name = 'Segoe UI'
          Font.Style = []
          ParentFont = False
        end
        object lblAvailableCellsValue: TLabel
          Left = 20
          Top = 40
          Width = 20
          Height = 25
          Caption = '12'
          Font.Charset = ANSI_CHARSET
          Font.Color = clLime
          Font.Height = -19
          Font.Name = 'Segoe UI'
          Font.Style = [fsBold]
          ParentFont = False
        end
      end
      object pnlOccupiedCells: TPanel
        Left = 560
        Top = 20
        Width = 250
        Height = 80
        BevelOuter = bvRaised
        Color = 4342338
        ParentBackground = False
        TabOrder = 2
        object lblOccupiedCells: TLabel
          Left = 20
          Top = 15
          Width = 100
          Height = 17
          Caption = #1575#1604#1593#1606#1575#1576#1585' '#1575#1604#1605#1588#1594#1608#1604#1577
          Font.Charset = ANSI_CHARSET
          Font.Color = clSilver
          Font.Height = -13
          Font.Name = 'Segoe UI'
          Font.Style = []
          ParentFont = False
        end
        object lblOccupiedCellsValue: TLabel
          Left = 20
          Top = 40
          Width = 20
          Height = 25
          Caption = '23'
          Font.Charset = ANSI_CHARSET
          Font.Color = clYellow
          Font.Height = -19
          Font.Name = 'Segoe UI'
          Font.Style = [fsBold]
          ParentFont = False
        end
      end
      object pnlAlerts: TPanel
        Left = 830
        Top = 20
        Width = 250
        Height = 80
        BevelOuter = bvRaised
        Color = 4342338
        ParentBackground = False
        TabOrder = 3
        object lblAlerts: TLabel
          Left = 20
          Top = 15
          Width = 60
          Height = 17
          Caption = #1575#1604#1578#1606#1576#1610#1607#1575#1578
          Font.Charset = ANSI_CHARSET
          Font.Color = clSilver
          Font.Height = -13
          Font.Name = 'Segoe UI'
          Font.Style = []
          ParentFont = False
        end
        object lblAlertsValue: TLabel
          Left = 20
          Top = 40
          Width = 10
          Height = 25
          Caption = '3'
          Font.Charset = ANSI_CHARSET
          Font.Color = clRed
          Font.Height = -19
          Font.Name = 'Segoe UI'
          Font.Style = [fsBold]
          ParentFont = False
        end
      end
    end
    object pnlChart: TPanel
      Left = 0
      Top = 120
      Width = 1116
      Height = 568
      Align = alClient
      BevelOuter = bvNone
      Color = 3289650
      ParentBackground = False
      TabOrder = 1
      object chartCells: TDBChart
        Left = 20
        Top = 20
        Width = 1076
        Height = 528
        BackWall.Brush.Color = clWhite
        BackWall.Brush.Style = bsClear
        BackWall.Pen.Visible = False
        Title.Text.Strings = (
          #1573#1581#1589#1575#1574#1610#1575#1578' '#1575#1604#1593#1606#1575#1576#1585)
        Title.Font.Color = clWhite
        Title.Font.Height = -16
        Title.Font.Style = [fsBold]
        BackColor = 3289650
        BottomAxis.Axis.Color = clSilver
        BottomAxis.Grid.Color = clGray
        BottomAxis.LabelsFont.Color = clWhite
        BottomAxis.Title.Font.Color = clWhite
        LeftAxis.Axis.Color = clSilver
        LeftAxis.Grid.Color = clGray
        LeftAxis.LabelsFont.Color = clWhite
        LeftAxis.Title.Font.Color = clWhite
        Legend.Font.Color = clWhite
        RightAxis.Axis.Color = clSilver
        RightAxis.Grid.Color = clGray
        TopAxis.Axis.Color = clSilver
        TopAxis.Grid.Color = clGray
        View3D = False
        TabOrder = 0
        DefaultCanvas = 'TGDIPlusCanvas'
        ColorPaletteIndex = 13
        object seriesCells: TBarSeries
          ColorEachPoint = True
          Marks.Arrow.Visible = True
          Marks.Callout.Brush.Color = clBlack
          Marks.Callout.Arrow.Visible = True
          Marks.Font.Color = clWhite
          Marks.Visible = True
          Title = #1593#1583#1583' '#1575#1604#1606#1586#1604#1575#1569
          XValues.Name = 'X'
          XValues.Order = loAscending
          YValues.Name = 'Bar'
          YValues.Order = loNone
        end
      end
    end
  end
  object ADOConnection1: TADOConnection
    LoginPrompt = False
    Left = 40
    Top = 200
  end
  object qCells: TADOQuery
    Connection = ADOConnection1
    Parameters = <>
    Left = 120
    Top = 200
  end
  object qStats: TADOQuery
    Connection = ADOConnection1
    Parameters = <>
    Left = 200
    Top = 200
  end
  object tmrUpdate: TTimer
    Interval = 1000
    OnTimer = tmrUpdateTimer
    Left = 280
    Top = 200
  end
  object ImageList1: TImageList
    Left = 360
    Top = 200
  end
end
