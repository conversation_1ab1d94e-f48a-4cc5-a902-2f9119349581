﻿object betweenstores: Tbetweenstores
  Left = 0
  Top = 0
  Caption = #1606#1602#1604' '#1576#1610#1606' '#1575#1604#1605#1581#1575#1586#1606
  ClientHeight = 694
  ClientWidth = 1021
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -13
  Font.Name = 'Arial'
  Font.Style = [fsBold]
  FormStyle = fsMDIChild
  OldCreateOrder = False
  Position = poDesktopCenter
  Visible = True
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  PixelsPerInch = 96
  TextHeight = 16
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 1021
    Height = 694
    Align = alClient
    BiDiMode = bdRightToLeft
    Caption = 'Panel1'
    ParentBiDiMode = False
    TabOrder = 0
    object Panel2: TPanel
      Left = 1
      Top = 1
      Width = 1019
      Height = 692
      Align = alClient
      TabOrder = 0
      object Splitter2: TSplitter
        Left = 1
        Top = 1
        Width = 0
        Height = 690
        ExplicitLeft = 153
        ExplicitHeight = 370
      end
      object Splitter3: TSplitter
        Left = 1015
        Top = 1
        Height = 690
        Align = alRight
        ExplicitLeft = 9
        ExplicitTop = 9
        ExplicitHeight = 370
      end
      object panelgrid: TPanel
        Left = 1
        Top = 1
        Width = 1014
        Height = 690
        Align = alClient
        Caption = 'panelgrid'
        TabOrder = 0
        object DBGridEh1: TDBGridEh
          Left = 1
          Top = 65
          Width = 1012
          Height = 533
          Align = alClient
          AutoFitColWidths = True
          BiDiMode = bdRightToLeft
          BorderStyle = bsNone
          Color = clWhite
          DataSource = dsmidtable
          DynProps = <>
          FooterRowCount = 1
          IndicatorOptions = [gioShowRowIndicatorEh, gioShowRecNoEh]
          OddRowColor = 16640761
          OptionsEh = [dghHighlightFocus, dghClearSelection, dghDialogFind, dghShowRecNo, dghColumnResize, dghColumnMove, dghExtendVertLines]
          ParentBiDiMode = False
          RowDetailPanel.Width = 450
          RowDetailPanel.ParentColor = True
          RowHeight = 30
          RowSizingAllowed = True
          SelectionDrawParams.DrawFocusFrame = False
          SelectionDrawParams.DrawFocusFrameStored = True
          SumList.Active = True
          TabOrder = 0
          TitleParams.Color = clWhite
          TitleParams.RowHeight = 30
          OnKeyPress = DBGridEh1KeyPress
          Columns = <
            item
              CellButtons = <>
              DynProps = <>
              EditButtons = <>
              FieldName = 'midok'
              Footers = <>
              Title.Caption = 'ok'
              Width = 33
            end
            item
              Alignment = taLeftJustify
              CellButtons = <>
              DynProps = <>
              EditButtons = <>
              FieldName = 'miditemname'
              Footer.ValueType = fvtCount
              Footers = <>
              ReadOnly = True
              Title.Alignment = taCenter
              Title.Caption = #1575#1587#1605' '#1575#1604#1589#1606#1601
              Width = 356
            end
            item
              Alignment = taCenter
              CellButtons = <>
              DynProps = <>
              EditButtons = <>
              FieldName = 'midquantity'
              Footers = <>
              ReadOnly = True
              Title.Alignment = taCenter
              Title.Caption = #1575#1604#1603#1605#1610#1577
              Visible = False
              Width = 51
            end
            item
              CellButtons = <>
              DynProps = <>
              EditButtons = <>
              FieldName = 'midprice'
              Footers = <>
              ReadOnly = True
              Title.Alignment = taCenter
              Title.Caption = #1575#1604#1587#1593#1585
              Visible = False
              Width = 71
            end
            item
              CellButtons = <>
              DynProps = <>
              EditButtons = <>
              FieldName = 'midtot'
              Footer.ValueType = fvtSum
              Footers = <>
              ReadOnly = True
              Title.Alignment = taCenter
              Title.Caption = #1575#1604#1575#1580#1605#1575#1604#1610
              Visible = False
              Width = 122
            end>
          object RowDetailData: TRowDetailPanelControlEh
          end
        end
        object Panel17: TPanel
          Left = 1
          Top = 1
          Width = 1012
          Height = 64
          Align = alTop
          Color = clWindow
          ParentBackground = False
          TabOrder = 1
          object Label1: TLabel
            Left = 682
            Top = 36
            Width = 41
            Height = 16
            Caption = #1605#1606' '#1605#1582#1586#1606
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = 'Arial'
            Font.Style = [fsBold]
            ParentFont = False
          end
          object Label24: TLabel
            Left = 59
            Top = 9
            Width = 42
            Height = 16
            Caption = #1578' '#1575#1604#1578#1587#1604#1610#1605
            Color = clWindowText
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Arial'
            Font.Style = [fsBold]
            ParentColor = False
            ParentFont = False
          end
          object Label3: TLabel
            Left = 933
            Top = 24
            Width = 30
            Height = 16
            Caption = #1575#1604#1578#1575#1585#1610#1582
          end
          object Label23: TLabel
            Left = 393
            Top = 42
            Width = 42
            Height = 16
            Caption = #1575#1604#1609' '#1605#1582#1586#1606
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = 'Arial'
            Font.Style = [fsBold]
            ParentFont = False
          end
          object DateTimePicker1: TDateTimePicker
            Left = 828
            Top = 24
            Width = 99
            Height = 27
            Date = 42101.983778599540000000
            Time = 42101.983778599540000000
            Color = 15856371
            Enabled = False
            TabOrder = 0
          end
          object DateTimePicker2: TDateTimePicker
            Left = 5
            Top = 7
            Width = 99
            Height = 27
            Date = 42823.792650833330000000
            Time = 42823.792650833330000000
            Color = 15856371
            TabOrder = 1
          end
          object cmbstoreto: TComboBox
            Left = 147
            Top = 31
            Width = 211
            Height = 27
            BiDiMode = bdRightToLeft
            Color = clWhite
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -16
            Font.Name = 'Arial'
            Font.Style = [fsBold]
            ParentBiDiMode = False
            ParentFont = False
            TabOrder = 2
            OnEnter = cmbstoretoEnter
            OnKeyPress = cmbstoretoKeyPress
          end
          object cmbstorefrom: TComboBox
            Left = 472
            Top = 34
            Width = 204
            Height = 24
            TabOrder = 3
            OnChange = cmbstorefromChange
            OnEnter = cmbstorefromEnter
            OnKeyPress = cmbstorefromKeyPress
          end
        end
        object Panel3: TPanel
          Left = 1
          Top = 598
          Width = 1012
          Height = 91
          Align = alBottom
          TabOrder = 2
          DesignSize = (
            1012
            91)
          object Label6: TLabel
            Left = 289
            Top = 63
            Width = 31
            Height = 16
            Caption = #1575#1604#1589#1575#1601#1610
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = 'Arial'
            Font.Style = [fsBold]
            ParentFont = False
            Visible = False
          end
          object Label2: TLabel
            Left = 287
            Top = 10
            Width = 35
            Height = 16
            Caption = #1575#1604#1575#1580#1605#1575#1604#1610
            Visible = False
          end
          object Label5: TLabel
            Left = 944
            Top = 19
            Width = 37
            Height = 16
            Anchors = [akTop, akRight]
            Caption = #1605#1604#1575#1581#1592#1575#1578
          end
          object edtnet: TEdit
            Left = 168
            Top = 60
            Width = 115
            Height = 27
            Color = clWhite
            Enabled = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -16
            Font.Name = 'Arial'
            Font.Style = [fsBold]
            ParentFont = False
            ReadOnly = True
            TabOrder = 0
            Text = '0'
            Visible = False
          end
          object edtinvnotes: TEdit
            Left = 406
            Top = 15
            Width = 532
            Height = 24
            Anchors = [akTop, akRight]
            TabOrder = 1
          end
          object ProgressBar2: TProgressBar
            Left = 384
            Top = 1
            Width = 596
            Height = 17
            Anchors = [akTop, akRight]
            TabOrder = 2
            Visible = False
          end
          object edttot: TEdit
            Left = 168
            Top = 2
            Width = 115
            Height = 27
            Enabled = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -16
            Font.Name = 'Arial'
            Font.Style = [fsBold]
            ParentFont = False
            ReadOnly = True
            TabOrder = 3
            Text = '0'
            Visible = False
          end
          object btnprintinv: TButton
            Left = 664
            Top = 45
            Width = 59
            Height = 35
            Caption = #1591' '#1575#1604#1601#1575#1578#1608#1585#1577
            TabOrder = 4
            Visible = False
            OnClick = btnprintinvClick
          end
          object btnexec: TButton
            Left = 15
            Top = 29
            Width = 130
            Height = 44
            Caption = 'btnexec'
            TabOrder = 5
            OnClick = btnexecClick
          end
          object CheckBox5: TCheckBox
            Left = 944
            Top = 53
            Width = 58
            Height = 17
            Caption = #1591#1576#1575#1593#1577
            Checked = True
            State = cbChecked
            TabOrder = 6
            Visible = False
          end
        end
      end
    end
  end
  object dsmidtable: TDataSource
    DataSet = tmid
    Left = 106
    Top = 106
  end
  object qmid: TADOQuery
    Parameters = <>
    SQL.Strings = (
      '] ('
      '[midtableid] [int] IDENTITY(1,1) NOT NULL,'
      #9'[miditemid] [int] NOT NULL,'
      #9'[miditemname] [nvarchar](150) NOT NULL,'
      #9'[midquantity] [numeric](12, 3) NOT NULL default 0,'
      #9'[midprice] [numeric](12, 3) NOT NULL default 0,'
      #9'[miditemcost] [numeric](14, 5) NOT NULL default 0,'
      #9'[midtot] [numeric](12, 3) NULL,'
      #9'[midstore] [nvarchar](75) NULL ,'
      #9'[midstoreno] [int] NULL,'
      #9'[midrowid] [int] NULL,'
      #9'[midok] [bit] NOT NULL,'
      #9'[midserialinxls] [int]  NULL,'
      #9'[midcontainer_invd_id] [int]  NULL,'
      #9'[midselltocustomer] [numeric](12, 3)  NULL ,'
      '  [mid_originalprice] [numeric](12, 3)  NULL ,'
      '  [midx] [int]  NULL,'
      ' CONSTRAINT [PK_midtbleidx'
      ''
      '')
    Left = 10
    Top = 36
  end
  object tmid: TADOTable
    ConnectionString = 
      'Provider=SQLNCLI11.1;Integrated Security=SSPI;Persist Security I' +
      'nfo=False;User ID="";Initial Catalog=mlt_db;Data Source=LAPTOP-G' +
      '4FOCJHM\SQL2017;Initial File Name="";Server SPN=""'
    CursorType = ctStatic
    OnCalcFields = tmidCalcFields
    TableName = 'midtablesalesinv'
    Left = 20
    Top = 106
    object tmidmidtableid: TAutoIncField
      FieldName = 'midtableid'
      ReadOnly = True
    end
    object tmidmiditemid: TIntegerField
      FieldName = 'miditemid'
    end
    object tmidmiditemname: TWideStringField
      FieldName = 'miditemname'
      Size = 150
    end
    object tmidmidquantity: TBCDField
      FieldName = 'midquantity'
      Precision = 8
      Size = 3
    end
    object tmidmidprice: TBCDField
      FieldName = 'midprice'
      Precision = 8
      Size = 3
    end
    object tmidmiditemcost: TFMTBCDField
      FieldName = 'miditemcost'
      Precision = 8
      Size = 5
    end
    object tmidmidstore: TWideStringField
      FieldName = 'midstore'
      Size = 75
    end
    object tmidmidstoreno: TIntegerField
      FieldName = 'midstoreno'
    end
    object tmidmidrowid: TIntegerField
      FieldName = 'midrowid'
    end
    object tmidmidok: TBooleanField
      FieldName = 'midok'
      OnChange = tmidmidokChange
    end
    object tmidmidserialinxls: TIntegerField
      FieldName = 'midserialinxls'
    end
    object tmidmidcontainer_invd_id: TIntegerField
      FieldName = 'midcontainer_invd_id'
    end
    object tmidmidselltocustomer: TBCDField
      FieldName = 'midselltocustomer'
      Precision = 8
      Size = 3
    end
    object tmidmid_originalprice: TBCDField
      FieldName = 'mid_originalprice'
      Precision = 8
      Size = 3
    end
    object tmidmidtot: TCurrencyField
      FieldKind = fkCalculated
      FieldName = 'midtot'
      Calculated = True
    end
    object tmidmidx: TIntegerField
      FieldName = 'midx'
    end
  end
  object qstores: TADOQuery
    Parameters = <>
    Left = 106
    Top = 55
  end
  object dsstores: TDataSource
    Left = 20
    Top = 149
  end
  object qsafenames: TADOQuery
    Parameters = <>
    Left = 68
    Top = 47
  end
  object qsafeservice: TADOQuery
    Parameters = <>
    Left = 31
    Top = 311
  end
  object qseller: TADOQuery
    Parameters = <>
    Left = 20
    Top = 231
  end
  object frxinv2: TfrxReport
    Version = '6.9.14'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Default'
    PrintOptions.PrintOnSheet = 0
    ReportOptions.CreateDate = 39810.598356840300000000
    ReportOptions.LastChange = 39810.598356840300000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      'begin'
      ''
      'end.')
    Left = 420
    Top = 208
    Datasets = <
      item
        DataSet = frxdsinv
        DataSetName = 'inv'
      end
      item
        DataSet = frmMain.frxDBDataset1
        DataSetName = 'ginfo'
      end>
    Variables = <
      item
        Name = ' New Category1'
        Value = Null
      end
      item
        Name = 'New Variable1'
        Value = '<New Variable1>'
      end
      item
        Name = 'New Variable2'
        Value = Null
      end
      item
        Name = 'New Variable3'
        Value = Null
      end
      item
        Name = 'New Variable4'
        Value = Null
      end
      item
        Name = 'New Variable5'
        Value = Null
      end
      item
        Name = 'New Variable6'
        Value = Null
      end
      item
        Name = 'New Variable7'
        Value = Null
      end
      item
        Name = 'New Variable8'
        Value = Null
      end
      item
        Name = 'New Variable9'
        Value = Null
      end
      item
        Name = 'New Variable10'
        Value = Null
      end
      item
        Name = 'New Variable11'
        Value = Null
      end
      item
        Name = 'New Variable12'
        Value = Null
      end
      item
        Name = 'New Variable13'
        Value = Null
      end
      item
        Name = 'New Variable14'
        Value = Null
      end
      item
        Name = 'New Variable15'
        Value = Null
      end
      item
        Name = 'New Variable16'
        Value = Null
      end
      item
        Name = 'New Variable17'
        Value = Null
      end
      item
        Name = 'New Variable18'
        Value = Null
      end
      item
        Name = 'New Variable19'
        Value = Null
      end
      item
        Name = 'New Variable20'
        Value = Null
      end
      item
        Name = 'New Variable21'
        Value = Null
      end
      item
        Name = 'New Variable22'
        Value = Null
      end
      item
        Name = 'New Variable23'
        Value = Null
      end
      item
        Name = 'New Variable24'
        Value = Null
      end
      item
        Name = 'New Variable25'
        Value = Null
      end
      item
        Name = 'New Variable26'
        Value = Null
      end
      item
        Name = 'New Variable27'
        Value = Null
      end
      item
        Name = 'New Variable28'
        Value = Null
      end
      item
        Name = 'vaddress'
        Value = Null
      end
      item
        Name = 'New Variable29'
        Value = Null
      end
      item
        Name = 'New Variable30'
        Value = Null
      end
      item
        Name = 'maincaption'
        Value = Null
      end
      item
        Name = 'subcaption'
        Value = Null
      end
      item
        Name = 'New Variable31'
        Value = Null
      end
      item
        Name = 'thenet'
        Value = Null
      end
      item
        Name = 'shipdate'
        Value = Null
      end
      item
        Name = 'thewanted'
        Value = Null
      end
      item
        Name = 'invtype'
        Value = Null
      end
      item
        Name = 'thebranch'
        Value = Null
      end>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -13
      Font.Name = 'Arial'
      Font.Style = [fsBold]
      PaperWidth = 210.000000000000000000
      PaperHeight = 297.000000000000000000
      PaperSize = 9
      Frame.Typ = []
      MirrorMode = []
      object Header1: TfrxHeader
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 157.401670000000000000
        Top = 234.330860000000000000
        Width = 793.701300000000000000
        object Memo6: TfrxMemoView
          AllowVectorExport = True
          Left = 355.779840000000000000
          Top = 127.165430000000000000
          Width = 355.275820000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1589#1606#1601)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo8: TfrxMemoView
          AllowVectorExport = True
          Left = 710.992580000000000000
          Top = 127.165430000000000000
          Width = 49.133890000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1578)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo10: TfrxMemoView
          AllowVectorExport = True
          Left = 265.787570000000000000
          Top = 127.165430000000000000
          Width = 90.708720000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1603#1605#1610#1577)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo11: TfrxMemoView
          AllowVectorExport = True
          Left = 167.283550000000000000
          Top = 127.165430000000000000
          Width = 98.267780000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1602#1610#1605#1577)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo12: TfrxMemoView
          AllowVectorExport = True
          Left = 50.795300000000000000
          Top = 127.165430000000000000
          Width = 117.165430000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1575#1580#1605#1575#1604#1610)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo32: TfrxMemoView
          AllowVectorExport = True
          Left = 132.283550000000000000
          Top = 68.574829999999900000
          Width = 37.795300000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Israr-Syria'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1578#1575#1585#1610#1582)
          ParentFont = False
        end
        object Memo34: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 68.574830000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DataSet = frxdsinv
          DataSetName = 'inv'
          DisplayFormat.FormatStr = 'dd/mm/yyyy'
          DisplayFormat.Kind = fkDateTime
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[inv."invm_date"]')
          ParentFont = False
        end
        object Memo35: TfrxMemoView
          AllowVectorExport = True
          Left = 343.937230000000000000
          Top = 56.574830000000000000
          Width = 154.960730000000000000
          Height = 22.677180000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Israr-Syria'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[invtype]')
          ParentFont = False
        end
        object Memo37: TfrxMemoView
          AllowVectorExport = True
          Left = 299.141930000000000000
          Top = 27.031540000000000000
          Width = 230.551330000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[New Variable3]')
          ParentFont = False
        end
        object Memo38: TfrxMemoView
          AllowVectorExport = True
          Left = 691.653990000000000000
          Top = 92.708719999999900000
          Width = 52.913420000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1575#1582#1608#1577)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo39: TfrxMemoView
          AllowVectorExport = True
          Left = 461.102660000000000000
          Top = 92.708720000000000000
          Width = 226.771800000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable5]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo31: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 100.811070000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable29]')
          ParentFont = False
        end
        object Memo41: TfrxMemoView
          AllowVectorExport = True
          Left = 132.283571970000000000
          Top = 100.811070000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1578#1575#1585#1610#1582' '#1575#1604#1578#1587#1604#1610#1605)
        end
        object Memo40: TfrxMemoView
          AllowVectorExport = True
          Left = 3.779530000000000000
          Width = 797.480830000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -21
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            
              '________________________________________________________________' +
              '__________________')
          ParentFont = False
        end
      end
      object MasterData1: TfrxMasterData
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 28.346456690000000000
        Top = 415.748300000000000000
        Width = 793.701300000000000000
        DataSet = frxdsinv
        DataSetName = 'inv'
        RowCount = 0
        Stretched = True
        object Memo1: TfrxMemoView
          AllowVectorExport = True
          Left = 355.748031500000000000
          Top = 0.338590000000000000
          Width = 355.275820000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          DataSet = frxdsinv
          DataSetName = 'inv'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haRight
          Memo.UTF8W = (
            '   [inv."invd_articlename"]')
          ParentFont = False
          RTLReading = True
          VAlign = vaCenter
          Formats = <
            item
            end
            item
            end>
        end
        object Memo3: TfrxMemoView
          AllowVectorExport = True
          Left = 265.708661420000000000
          Top = 0.338590000000000000
          Width = 90.708661420000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          DataSet = frxdsinv
          DataSetName = 'inv'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[inv."invd_invqty"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo4: TfrxMemoView
          AllowVectorExport = True
          Left = 167.283550000000000000
          Top = 0.338590000000000000
          Width = 98.267780000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          DataSet = frxdsinv
          DataSetName = 'inv'
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[inv."invd_invprice"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo5: TfrxMemoView
          AllowVectorExport = True
          Left = 50.795300000000000000
          Top = 0.338590000000000000
          Width = 117.165430000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          DataSet = frxdsinv
          DataSetName = 'inv'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[inv."invd_tot"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo7: TfrxMemoView
          AllowVectorExport = True
          Left = 710.874015750000000000
          Top = 0.338590000000011000
          Width = 49.133890000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[Line]')
          ParentFont = False
          VAlign = vaCenter
        end
      end
      object Footer1: TfrxFooter
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 177.637910000000000000
        Top = 468.661720000000000000
        Width = 793.701300000000000000
        object Memo28: TfrxMemoView
          AllowVectorExport = True
          Left = 228.260050000000000000
          Top = 41.472480000000000000
          Width = 529.134200000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '   [New Variable15]')
          ParentFont = False
          RTLReading = True
          VAlign = vaCenter
        end
        object Memo29: TfrxMemoView
          AllowVectorExport = True
          Left = 231.244280000000000000
          Top = 154.622140000000000000
          Width = 49.133890000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1578#1608#1602#1610#1593)
          ParentFont = False
        end
        object Memo30: TfrxMemoView
          AllowVectorExport = True
          Left = 76.283550000000000000
          Top = 154.622140000000000000
          Width = 147.401670000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '......................................')
          ParentFont = False
        end
        object Memo33: TfrxMemoView
          AllowVectorExport = True
          Left = 166.283550000000000000
          Top = 41.574830000000000000
          Width = 49.133890000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ': '#1575#1604#1582#1589#1605)
          ParentFont = False
        end
        object Memo36: TfrxMemoView
          AllowVectorExport = True
          Left = 67.472480000000000000
          Top = 41.574830000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable16]')
          ParentFont = False
        end
        object Memo43: TfrxMemoView
          AllowVectorExport = True
          Left = 50.653543310000000000
          Top = 1.692950000000000000
          Width = 117.165430000000000000
          Height = 30.236240000000000000
          DisplayFormat.FormatStr = '%2.2n'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clWindow
          HAlign = haCenter
          Memo.UTF8W = (
            '[SUM(<inv."invd_tot">,MasterData1)]')
          ParentFont = False
        end
        object Memo47: TfrxMemoView
          AllowVectorExport = True
          Left = 171.440944880000000000
          Top = 4.692950000000000000
          Width = 60.472480000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Israr-Syria'
          Font.Style = []
          Frame.Typ = []
          Memo.UTF8W = (
            #1575#1604#1575#1580#1605#1575#1604#1610)
          ParentFont = False
        end
        object Memo44: TfrxMemoView
          AllowVectorExport = True
          Left = 717.331170000000000000
          Top = 77.047310000000000000
          Width = 45.354360000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1605#1583#1601#1608#1593)
          ParentFont = False
        end
        object Memo46: TfrxMemoView
          AllowVectorExport = True
          Left = 599.622450000000000000
          Top = 77.047310000000000000
          Width = 113.385900000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable31]')
          ParentFont = False
        end
        object Memo2: TfrxMemoView
          AllowVectorExport = True
          Left = 67.472480000000000000
          Top = 66.811070000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thenet]')
          ParentFont = False
        end
        object Memo9: TfrxMemoView
          AllowVectorExport = True
          Left = 166.283550000000000000
          Top = 66.811070000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1589#1575#1601#1610)
          ParentFont = False
        end
        object Memo13: TfrxMemoView
          AllowVectorExport = True
          Left = 430.866420000000000000
          Top = 137.637910000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Visible = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[shipdate]')
          ParentFont = False
        end
        object Memo15: TfrxMemoView
          AllowVectorExport = True
          Left = 483.779840000000000000
          Top = 137.637910000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Visible = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            ':'#1578#1575#1585#1610#1582' '#1575#1604#1575#1587#1578#1604#1575#1605)
          ParentFont = False
        end
        object Memo17: TfrxMemoView
          AllowVectorExport = True
          Left = 717.331170000000000000
          Top = 100.826840000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1605#1587#1578#1581#1602)
        end
        object Memo18: TfrxMemoView
          AllowVectorExport = True
          Left = 618.520100000000000000
          Top = 100.826840000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thewanted]')
          ParentFont = False
        end
      end
      object PageFooter1: TfrxPageFooter
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 98.267780000000000000
        Top = 706.772110000000000000
        Width = 793.701300000000000000
        object Memo20: TfrxMemoView
          AllowVectorExport = True
          Top = 9.220469999999980000
          Width = 793.701300000000000000
          Height = 7.559060000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -21
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftTop]
          HAlign = haCenter
          ParentFont = False
        end
        object Memo14: TfrxMemoView
          AllowVectorExport = True
          Left = 15.118120000000000000
          Top = 26.456710000000000000
          Width = 755.906000000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[vaddress]')
          ParentFont = False
          RTLReading = True
        end
        object Memo16: TfrxMemoView
          AllowVectorExport = True
          Left = 15.118120000000000000
          Top = 60.472480000000000000
          Width = 755.906000000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable26]')
          ParentFont = False
        end
      end
      object PageHeader1: TfrxPageHeader
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -13
        Font.Name = 'Batang'
        Font.Style = [fsBold]
        Height = 154.960730000000000000
        ParentFont = False
        Top = 18.897650000000000000
        Width = 793.701300000000000000
        object Memo42: TfrxMemoView
          AllowVectorExport = True
          Left = 358.716760000000000000
          Top = 22.677180000000000000
          Width = 430.866420000000000000
          Height = 34.015770000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -27
          Font.Name = 'arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[maincaption]')
          ParentFont = False
        end
        object Memo45: TfrxMemoView
          AllowVectorExport = True
          Left = 431.866420000000000000
          Top = 79.826840000000000000
          Width = 302.362400000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Batang'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[subcaption]')
          ParentFont = False
        end
        object Memo50: TfrxMemoView
          AllowVectorExport = True
          Left = 445.984540000000000000
          Top = 113.385900000000000000
          Width = 279.685220000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Batang'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[thebranch]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Picture1: TfrxPictureView
          AllowVectorExport = True
          Left = 60.472480000000000000
          Top = 10.118120000000000000
          Width = 215.433210000000000000
          Height = 136.063080000000000000
          DataField = 'mylogo1'
          DataSet = frmMain.frxDBDataset1
          DataSetName = 'ginfo'
          Frame.Typ = []
          HightQuality = False
          Transparent = False
          TransparentColor = clWhite
        end
      end
    end
  end
  object frxdsinv: TfrxDBDataset
    UserName = 'inv'
    CloseDataSource = False
    DataSet = tmid
    BCDToCurrency = False
    Left = 466
    Top = 370
  end
  object frxDBDataset1: TfrxDBDataset
    UserName = 'detail'
    CloseDataSource = False
    BCDToCurrency = False
    Left = 464
    Top = 151
  end
  object ArabicLingualAmount1: TArabicLingualAmount
    Number = '0'
    Prefix = #1601#1602#1591' '
    Suffix = ' '#1604#1575' '#1594#1610#1585
    Currency.Gender = مذكر
    Currency.InSingle = #1583#1610#1606#1575#1585
    Currency.InPlural = #1583#1606#1575#1606#1610#1585
    Currency.ToOriginCountry = #1604#1610#1576#1610
    Currency.FracGender = مذكر
    Currency.FracInSingle = #1583#1585#1607#1605
    Currency.FracInPlural = #1583#1585#1575#1607#1605
    Currency.FracsInUnit = 1000
    Currency.Country = Custom
    Left = 20
    Top = 278
  end
  object frxinv3: TfrxReport
    Version = '6.9.14'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Default'
    PrintOptions.PrintOnSheet = 0
    ReportOptions.CreateDate = 39810.598356840300000000
    ReportOptions.LastChange = 39810.598356840300000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      'begin'
      ''
      'end.')
    Left = 412
    Top = 344
    Datasets = <
      item
        DataSet = frmMain.frxDBDataset2
        DataSetName = 'branch'
      end
      item
        DataSet = frmMain.frxDBDataset1
        DataSetName = 'ginfo'
      end
      item
        DataSet = frxdsinv
        DataSetName = 'inv'
      end>
    Variables = <
      item
        Name = ' New Category1'
        Value = Null
      end
      item
        Name = 'New Variable1'
        Value = '<New Variable1>'
      end
      item
        Name = 'New Variable2'
        Value = Null
      end
      item
        Name = 'New Variable3'
        Value = Null
      end
      item
        Name = 'New Variable4'
        Value = Null
      end
      item
        Name = 'New Variable5'
        Value = Null
      end
      item
        Name = 'New Variable6'
        Value = Null
      end
      item
        Name = 'New Variable7'
        Value = Null
      end
      item
        Name = 'New Variable8'
        Value = Null
      end
      item
        Name = 'New Variable9'
        Value = Null
      end
      item
        Name = 'New Variable10'
        Value = Null
      end
      item
        Name = 'New Variable11'
        Value = Null
      end
      item
        Name = 'New Variable12'
        Value = Null
      end
      item
        Name = 'New Variable13'
        Value = Null
      end
      item
        Name = 'New Variable14'
        Value = Null
      end
      item
        Name = 'New Variable15'
        Value = Null
      end
      item
        Name = 'New Variable16'
        Value = Null
      end
      item
        Name = 'New Variable17'
        Value = Null
      end
      item
        Name = 'New Variable18'
        Value = Null
      end
      item
        Name = 'New Variable19'
        Value = Null
      end
      item
        Name = 'New Variable20'
        Value = Null
      end
      item
        Name = 'New Variable21'
        Value = Null
      end
      item
        Name = 'New Variable22'
        Value = Null
      end
      item
        Name = 'New Variable23'
        Value = Null
      end
      item
        Name = 'New Variable24'
        Value = Null
      end
      item
        Name = 'New Variable25'
        Value = Null
      end
      item
        Name = 'New Variable26'
        Value = Null
      end
      item
        Name = 'New Variable27'
        Value = Null
      end
      item
        Name = 'New Variable28'
        Value = Null
      end
      item
        Name = 'vaddress'
        Value = Null
      end
      item
        Name = 'New Variable29'
        Value = Null
      end
      item
        Name = 'New Variable30'
        Value = Null
      end
      item
        Name = 'maincaption'
        Value = Null
      end
      item
        Name = 'subcaption'
        Value = Null
      end
      item
        Name = 'New Variable31'
        Value = Null
      end
      item
        Name = 'thenet'
        Value = Null
      end
      item
        Name = 'shipdate'
        Value = Null
      end
      item
        Name = 'thewanted'
        Value = Null
      end
      item
        Name = 'invtype'
        Value = Null
      end
      item
        Name = 'thebranch'
        Value = Null
      end
      item
        Name = 'thepayed'
        Value = ''
      end
      item
        Name = 'thediscount'
        Value = ''
      end
      item
        Name = 'thedate'
        Value = ''
      end
      item
        Name = 'theshipdate'
        Value = ''
      end
      item
        Name = 'thecustname'
        Value = ''
      end>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -13
      Font.Name = 'Arial'
      Font.Style = [fsBold]
      PaperWidth = 210.000000000000000000
      PaperHeight = 297.000000000000000000
      PaperSize = 9
      Frame.Typ = []
      MirrorMode = []
      object Header1: TfrxHeader
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 157.401670000000000000
        Top = 234.330860000000000000
        Width = 793.701300000000000000
        object Memo6: TfrxMemoView
          AllowVectorExport = True
          Left = 355.779840000000000000
          Top = 127.165430000000000000
          Width = 355.275820000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1589#1606#1601)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo8: TfrxMemoView
          AllowVectorExport = True
          Left = 710.992580000000000000
          Top = 127.165430000000000000
          Width = 49.133890000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1578)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo10: TfrxMemoView
          AllowVectorExport = True
          Left = 265.787570000000000000
          Top = 127.165430000000000000
          Width = 90.708720000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1603#1605#1610#1577)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo11: TfrxMemoView
          AllowVectorExport = True
          Left = 167.283550000000000000
          Top = 127.165430000000000000
          Width = 98.267780000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1602#1610#1605#1577)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo12: TfrxMemoView
          AllowVectorExport = True
          Left = 50.795300000000000000
          Top = 127.165430000000000000
          Width = 117.165430000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clScrollBar
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1604#1575#1580#1605#1575#1604#1610)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo32: TfrxMemoView
          AllowVectorExport = True
          Left = 132.283550000000000000
          Top = 68.574829999999900000
          Width = 37.795300000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Israr-Syria'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1578#1575#1585#1610#1582)
          ParentFont = False
        end
        object Memo34: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000010000
          Top = 68.574829999999990000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DataSet = frxdsinv
          DataSetName = 'inv'
          DisplayFormat.FormatStr = 'dd/mm/yyyy'
          DisplayFormat.Kind = fkDateTime
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thedate]')
          ParentFont = False
        end
        object Memo35: TfrxMemoView
          AllowVectorExport = True
          Left = 336.378170000000000000
          Top = 56.574829999999990000
          Width = 154.960730000000000000
          Height = 22.677180000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Israr-Syria'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[invtype]'
            '')
          ParentFont = False
        end
        object Memo37: TfrxMemoView
          AllowVectorExport = True
          Left = 299.141930000000000000
          Top = 27.031539999999980000
          Width = 230.551330000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            ' [New Variable3] '#1601#1575#1578#1608#1585#1577' '#1585#1602#1605' '
            '')
          ParentFont = False
        end
        object Memo38: TfrxMemoView
          AllowVectorExport = True
          Left = 691.653990000000000000
          Top = 92.708719999999900000
          Width = 52.913420000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1575#1582#1608#1577)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo39: TfrxMemoView
          AllowVectorExport = True
          Left = 461.102660000000000000
          Top = 92.708720000000000000
          Width = 226.771800000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thecustname]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo31: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000010000
          Top = 100.811070000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[theshipdate]'
            '')
          ParentFont = False
        end
        object Memo41: TfrxMemoView
          AllowVectorExport = True
          Left = 132.283571970000000000
          Top = 100.811070000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1578#1575#1585#1610#1582' '#1575#1604#1578#1587#1604#1610#1605)
        end
        object Memo40: TfrxMemoView
          AllowVectorExport = True
          Left = 3.779530000000000000
          Width = 797.480830000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -21
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            
              '________________________________________________________________' +
              '__________________')
          ParentFont = False
        end
      end
      object MasterData1: TfrxMasterData
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 28.346456690000000000
        Top = 415.748300000000000000
        Width = 793.701300000000000000
        DataSet = frxdsinv
        DataSetName = 'inv'
        RowCount = 0
        Stretched = True
        object Memo1: TfrxMemoView
          AllowVectorExport = True
          Left = 355.748031500000000000
          Top = 0.338590000000010600
          Width = 355.275820000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haRight
          Memo.UTF8W = (
            '[inv."miditemname"]')
          ParentFont = False
          RTLReading = True
          VAlign = vaCenter
        end
        object Memo3: TfrxMemoView
          AllowVectorExport = True
          Left = 265.708661420000000000
          Top = 0.338590000000010600
          Width = 90.708661420000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[inv."midquantity"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo4: TfrxMemoView
          AllowVectorExport = True
          Left = 167.283550000000000000
          Top = 0.338590000000010600
          Width = 98.267780000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[inv."midprice"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo5: TfrxMemoView
          AllowVectorExport = True
          Left = 50.795300000000000000
          Top = 0.338590000000010600
          Width = 117.165430000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[inv."midtot"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo7: TfrxMemoView
          AllowVectorExport = True
          Left = 710.874015750000000000
          Top = 0.338590000000011000
          Width = 49.133890000000000000
          Height = 26.456710000000000000
          StretchMode = smMaxHeight
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            '[Line]')
          ParentFont = False
          VAlign = vaCenter
        end
      end
      object Footer1: TfrxFooter
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 177.637910000000000000
        Top = 468.661720000000000000
        Width = 793.701300000000000000
        object Memo28: TfrxMemoView
          AllowVectorExport = True
          Left = 228.260050000000000000
          Top = 41.472480000000020000
          Width = 529.134199999999900000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '   [New Variable15]')
          ParentFont = False
          RTLReading = True
          VAlign = vaCenter
        end
        object Memo29: TfrxMemoView
          AllowVectorExport = True
          Left = 231.244280000000000000
          Top = 154.622140000000000000
          Width = 49.133890000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1578#1608#1602#1610#1593)
          ParentFont = False
        end
        object Memo30: TfrxMemoView
          AllowVectorExport = True
          Left = 76.283550000000000000
          Top = 154.622140000000000000
          Width = 147.401670000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '......................................')
          ParentFont = False
        end
        object Memo33: TfrxMemoView
          AllowVectorExport = True
          Left = 166.283550000000000000
          Top = 41.574830000000000000
          Width = 49.133890000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ': '#1575#1604#1582#1589#1605)
          ParentFont = False
        end
        object Memo36: TfrxMemoView
          AllowVectorExport = True
          Left = 67.472480000000000000
          Top = 41.574830000000020000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thediscount]')
          ParentFont = False
        end
        object Memo43: TfrxMemoView
          AllowVectorExport = True
          Left = 50.653543310000010000
          Top = 1.692949999999996000
          Width = 117.165430000000000000
          Height = 30.236240000000000000
          DisplayFormat.FormatStr = '%2.2n'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Fill.BackColor = clWindow
          HAlign = haCenter
          Memo.UTF8W = (
            '[SUM(<inv."midtot">,MasterData1)]')
          ParentFont = False
        end
        object Memo47: TfrxMemoView
          AllowVectorExport = True
          Left = 171.440944880000000000
          Top = 4.692950000000000000
          Width = 60.472480000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Israr-Syria'
          Font.Style = []
          Frame.Typ = []
          Memo.UTF8W = (
            #1575#1604#1575#1580#1605#1575#1604#1610)
          ParentFont = False
        end
        object Memo44: TfrxMemoView
          AllowVectorExport = True
          Left = 717.331170000000000000
          Top = 77.047310000000000000
          Width = 45.354360000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1605#1583#1601#1608#1593)
          ParentFont = False
        end
        object Memo46: TfrxMemoView
          AllowVectorExport = True
          Left = 599.622450000000000000
          Top = 77.047309999999990000
          Width = 113.385900000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thepayed]')
          ParentFont = False
        end
        object Memo2: TfrxMemoView
          AllowVectorExport = True
          Left = 67.472480000000000000
          Top = 66.811070000000030000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thenet]')
          ParentFont = False
        end
        object Memo9: TfrxMemoView
          AllowVectorExport = True
          Left = 166.283550000000000000
          Top = 66.811070000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1589#1575#1601#1610)
          ParentFont = False
        end
        object Memo13: TfrxMemoView
          AllowVectorExport = True
          Left = 430.866420000000000000
          Top = 137.637910000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Visible = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[theshipdate]')
          ParentFont = False
        end
        object Memo15: TfrxMemoView
          AllowVectorExport = True
          Left = 483.779840000000000000
          Top = 137.637910000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Visible = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            ':'#1578#1575#1585#1610#1582' '#1575#1604#1575#1587#1578#1604#1575#1605)
          ParentFont = False
        end
        object Memo17: TfrxMemoView
          AllowVectorExport = True
          Left = 717.331170000000000000
          Top = 100.826840000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1605#1587#1578#1581#1602)
        end
        object Memo18: TfrxMemoView
          AllowVectorExport = True
          Left = 618.520100000000000000
          Top = 100.826840000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          DisplayFormat.FormatStr = '%2.3f'
          DisplayFormat.Kind = fkNumeric
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[thewanted]')
          ParentFont = False
        end
      end
      object PageFooter1: TfrxPageFooter
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 98.267780000000000000
        Top = 706.772110000000000000
        Width = 793.701300000000000000
        object Memo20: TfrxMemoView
          AllowVectorExport = True
          Top = 9.220469999999980000
          Width = 793.701300000000000000
          Height = 7.559060000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -21
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftTop]
          HAlign = haCenter
          ParentFont = False
        end
        object Memo14: TfrxMemoView
          AllowVectorExport = True
          Left = 15.118120000000000000
          Top = 26.456710000000000000
          Width = 755.906000000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[vaddress]')
          ParentFont = False
          RTLReading = True
        end
        object Memo16: TfrxMemoView
          AllowVectorExport = True
          Left = 15.118120000000000000
          Top = 60.472480000000000000
          Width = 755.906000000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable26]')
          ParentFont = False
        end
      end
      object PageHeader1: TfrxPageHeader
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -13
        Font.Name = 'Batang'
        Font.Style = [fsBold]
        Height = 154.960730000000000000
        ParentFont = False
        Top = 18.897650000000000000
        Width = 793.701300000000000000
        object Memo42: TfrxMemoView
          AllowVectorExport = True
          Left = 358.716760000000000000
          Top = 22.677180000000000000
          Width = 430.866420000000000000
          Height = 34.015770000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -27
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[ginfo."gencompanyname"]')
          ParentFont = False
        end
        object Memo45: TfrxMemoView
          AllowVectorExport = True
          Left = 420.527830000000000000
          Top = 72.267780000000000000
          Width = 302.362400000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -21
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[ginfo."gensubcompanyname"]')
          ParentFont = False
        end
        object Memo50: TfrxMemoView
          AllowVectorExport = True
          Left = 445.984540000000000000
          Top = 113.385900000000000000
          Width = 279.685220000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Batang'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[branch."br_name"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Picture1: TfrxPictureView
          AllowVectorExport = True
          Left = 38.236240000000000000
          Top = 2.559060000000000000
          Width = 321.260050000000000000
          Height = 207.874150000000000000
          DataField = 'mylogo1'
          DataSet = frmMain.frxDBDataset1
          DataSetName = 'ginfo'
          Frame.Typ = []
          HightQuality = False
          Transparent = False
          TransparentColor = clWhite
        end
      end
    end
  end
  object frxrec: TfrxReport
    Version = '6.9.14'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Default'
    PrintOptions.PrintOnSheet = 0
    ReportOptions.CreateDate = 42358.379042511600000000
    ReportOptions.LastChange = 42358.379042511600000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      'begin'
      ''
      'end.')
    Left = 471
    Top = 443
    Datasets = <
      item
        DataSet = frmMain.frxDBDataset2
        DataSetName = 'branch'
      end
      item
        DataSet = frmMain.frxDBDataset1
        DataSetName = 'ginfo'
      end>
    Variables = <
      item
        Name = ' New Category1'
        Value = Null
      end
      item
        Name = 'New Variable1'
        Value = Null
      end
      item
        Name = 'New Variable2'
        Value = Null
      end
      item
        Name = 'New Variable3'
        Value = Null
      end
      item
        Name = 'New Variable4'
        Value = Null
      end
      item
        Name = 'New Variable5'
        Value = Null
      end
      item
        Name = 'New Variable6'
        Value = Null
      end
      item
        Name = 'New Variable7'
        Value = Null
      end
      item
        Name = 'New Variable8'
        Value = Null
      end
      item
        Name = 'maincaptio'
        Value = Null
      end
      item
        Name = 'subcaption'
        Value = Null
      end
      item
        Name = 'custname'
        Value = Null
      end
      item
        Name = 'thebranch'
        Value = Null
      end
      item
        Name = 'thevalue'
        Value = ''
      end
      item
        Name = 'therecno'
        Value = ''
      end
      item
        Name = 'theusername'
        Value = ''
      end
      item
        Name = 'thedate'
        Value = ''
      end>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      PaperWidth = 210.000000000000000000
      PaperHeight = 297.000000000000000000
      PaperSize = 9
      LeftMargin = 10.000000000000000000
      RightMargin = 10.000000000000000000
      TopMargin = 10.000000000000000000
      BottomMargin = 10.000000000000000000
      Frame.Typ = []
      MirrorMode = []
      object ReportTitle1: TfrxReportTitle
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 476.220780000000000000
        Top = 18.897650000000000000
        Width = 718.110700000000000000
        object Shape1: TfrxShapeView
          AllowVectorExport = True
          Left = 7.559060000000000000
          Top = 1.000000000000000000
          Width = 699.213050000000000000
          Height = 461.102660000000000000
          Frame.Typ = []
        end
        object Memo2: TfrxMemoView
          AllowVectorExport = True
          Left = 35.236240000000000000
          Top = 168.811070000000000000
          Width = 117.165430000000000000
          Height = 18.897650000000000000
          DataSetName = 'rec'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clWhite
          HAlign = haRight
          Memo.UTF8W = (
            '[therecno]')
          ParentFont = False
        end
        object Memo3: TfrxMemoView
          AllowVectorExport = True
          Left = 67.913420000000000000
          Top = 194.590600000000000000
          Width = 83.149660000000000000
          Height = 18.897650000000000000
          DataSetName = 'rec'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clWhite
          HAlign = haRight
          Memo.UTF8W = (
            '[thevalue]')
          ParentFont = False
        end
        object Memo5: TfrxMemoView
          AllowVectorExport = True
          Left = 153.283550000000000000
          Top = 169.590600000000000000
          Width = 79.370130000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1585#1602#1605' '#1575#1604#1575#1610#1589#1575#1604)
          ParentFont = False
        end
        object Memo6: TfrxMemoView
          AllowVectorExport = True
          Left = 153.283550000000000000
          Top = 222.504020000000000000
          Width = 79.370130000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1578#1575#1585#1610#1582)
          ParentFont = False
        end
        object Memo7: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000010000
          Top = 222.504020000000000000
          Width = 117.165430000000000000
          Height = 18.897650000000000000
          DataSetName = 'rec'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clWhite
          HAlign = haRight
          Memo.UTF8W = (
            '[thedate]')
          ParentFont = False
        end
        object Memo10: TfrxMemoView
          AllowVectorExport = True
          Left = 153.283550000000000000
          Top = 196.047310000000000000
          Width = 79.370130000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1605#1576#1604#1594' '#1575#1604#1605#1587#1578#1604#1605)
          ParentFont = False
        end
        object Memo15: TfrxMemoView
          AllowVectorExport = True
          Left = 143.401670000000000000
          Top = 399.362400000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1605#1587#1578#1604#1605)
          ParentFont = False
        end
        object Memo16: TfrxMemoView
          AllowVectorExport = True
          Left = 26.456710000000000000
          Top = 399.362400000000000000
          Width = 113.385900000000000000
          Height = 18.897650000000000000
          DataSetName = 'rec'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[theusername]')
          ParentFont = False
        end
        object Memo17: TfrxMemoView
          AllowVectorExport = True
          Left = 143.401670000000000000
          Top = 429.598640000000000000
          Width = 94.488250000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1578#1608#1602#1610#1593)
          ParentFont = False
        end
        object Memo1: TfrxMemoView
          AllowVectorExport = True
          Left = 340.157700000000000000
          Top = 403.205010000000000000
          Width = 359.055350000000000000
          Height = 34.015770000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -19
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[New Variable6]')
          ParentFont = False
        end
        object Memo4: TfrxMemoView
          AllowVectorExport = True
          Left = 317.480520000000000000
          Top = 152.811070000000000000
          Width = 185.196970000000000000
          Height = 22.677180000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -19
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            #1575#1610#1589#1575#1604' '#1575#1587#1578#1604#1575#1605' '#1605#1576#1604#1594' '#1605#1575#1604#1610)
          ParentFont = False
        end
        object Memo8: TfrxMemoView
          AllowVectorExport = True
          Left = 608.504330000000000000
          Top = 263.299320000000000000
          Width = 71.811070000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1587#1578#1604#1605#1578' '#1605#1606)
          ParentFont = False
        end
        object Memo11: TfrxMemoView
          AllowVectorExport = True
          Left = 608.504330000000000000
          Top = 301.094620000000000000
          Width = 75.590600000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1605#1576#1604#1594#1575' '#1608#1602#1583#1585#1607)
          ParentFont = False
        end
        object Memo12: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 301.094620000000000000
          Width = 574.488560000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clSilver
          HAlign = haRight
          ParentFont = False
        end
        object Memo13: TfrxMemoView
          AllowVectorExport = True
          Left = 608.504330000000000000
          Top = 342.669450000000000000
          Width = 56.692950000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            ':'#1575#1604#1576#1610#1575#1606)
          ParentFont = False
        end
        object Memo14: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 342.669450000000000000
          Width = 574.488188980000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clSilver
          HAlign = haRight
          ParentFont = False
        end
        object Memo19: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 301.094620000000000000
          Width = 574.488560000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clWhite
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable1]')
          ParentFont = False
        end
        object Memo20: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 342.669450000000000000
          Width = 574.488188980000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clWhite
          HAlign = haRight
          Memo.UTF8W = (
            '[New Variable2]')
          ParentFont = False
        end
        object Memo9: TfrxMemoView
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 263.299320000000000000
          Width = 574.488560000000000000
          Height = 18.897650000000000000
          DataSetName = 'rec'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clWhite
          HAlign = haRight
          Memo.UTF8W = (
            '[custname]')
          ParentFont = False
        end
        object Memo18: TfrxMemoView
          AllowVectorExport = True
          Left = 35.236240000000000000
          Top = 194.267780000000000000
          Width = 34.015770000000010000
          Height = 18.897650000000000000
          DataSetName = 'rec'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Fill.BackColor = clWhite
          Memo.UTF8W = (
            '[New Variable8]')
          ParentFont = False
        end
        object Memo42: TfrxMemoView
          AllowVectorExport = True
          Left = 424.645950000000000000
          Top = 12.677180000000000000
          Width = 245.669450000000000000
          Height = 34.015770000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[ginfo."gencompanyname"]')
          ParentFont = False
        end
        object Memo45: TfrxMemoView
          AllowVectorExport = True
          Left = 410.866420000000000000
          Top = 47.708720000000000000
          Width = 272.126160000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[ginfo."gensubcompanyname"]')
          ParentFont = False
        end
        object Memo50: TfrxMemoView
          AllowVectorExport = True
          Left = 407.968770000000000000
          Top = 79.370130000000000000
          Width = 279.685220000000000000
          Height = 26.456710000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Batang'
          Font.Style = [fsBold]
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            '[branch."br_name"]')
          ParentFont = False
          VAlign = vaCenter
        end
        object Picture1: TfrxPictureView
          AllowVectorExport = True
          Left = 35.677180000000000000
          Top = 7.118120000000000000
          Width = 238.110390000000000000
          Height = 147.401670000000000000
          DataField = 'mylogo1'
          DataSet = frmMain.frxDBDataset1
          DataSetName = 'ginfo'
          Frame.Typ = []
          HightQuality = False
          Transparent = False
          TransparentColor = clWhite
        end
      end
    end
  end
  object frxDBDataset2: TfrxDBDataset
    UserName = 'rec'
    CloseDataSource = False
    BCDToCurrency = False
    Left = 488
    Top = 495
  end
end
