unit Unit3;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, System.ImageList,
  Vcl.ImgList, Vcl.Menus, frxClass, frxDBSet, Data.DB, Data.Win.ADODB;

type
  TForm3 = class(TForm)
    Button1: TButton;
    Button2: TButton;
    Edit1: TEdit;
    ll: TLabel;
    Label1: TLabel;
    ADOConnection1: TADOConnection;
    ADOConnection2: TADOConnection;
    qbranch: TADOQuery;
    qinfo: TADOQuery;
    frxDBDataset2: TfrxDBDataset;
    qpermissions: TADOQuery;
    qstorenames: TADOQuery;
    frxDBDataset1: TfrxDBDataset;
    ImageList1: TImageList;
    qgen: TADOQuery;

    qsafenames: TADOQuery;

    OpenDialog1: TOpenDialog;
    ComboBox1: TComboBox;

    procedure Button1Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  Form3: TForm3;

implementation

{$R *.dfm}

procedure TForm3.Button1Click(Sender: TObject);
begin
q: TADOQuery;
conn: TADOConnection;
begin
if Key = Char(VK_RETURN) then
begin
Key := #0;
begin
checkinternet;
try
try
conn := TADOConnection.Create(nil);
conn.LoginPrompt := False;
q := TADOQuery.Create(nil);
conn.ConnectionString := theconnectionstring;
conn.Open();
q.Connection := conn;
q.SQL.Text := 'select * from permissions where  theusername=' + '''' +
Trim(ComboBox1.Text) + '''' + 'and thepassword=' + '''' +
Trim(Edit1.Text) + '''';
q.Open;
if q.IsEmpty then
begin
ShowMessage('كلمة المرور غير صحيحية');
Edit1.Text := '';
end
else
begin

theusername := Trim(q['theusername']);
theuserid := q['perid'];
Panel1.Visible := False;
cangivepermissions := q['cangivepermissions'];
canprint := q['canprint'];
canaltercash := q['canaltercash'];
canalterfin := q['canalterfin'];
canregfin := q['canregfin'];
canpaycash := q['canpaycash'];
canreadfin := q['canreadfin'];
canread := q['canread'];
cansell := q['cansell'];
canrecievecash := q['canrecievecash'];
canaltersells := q['canaltersells'];
canreadcash := q['canreadcash'];


           // Constraints.MinHeight := 600;
           // Constraints.MinWidth := 1000;

q.SQL.Text := 'select * from generaltable';

//
end;
except
on E: exception do
ShowMessage(E.Message);
end;
finally
q.Free;
conn.Free;
end;
end;

end;
end;
end;

end.
