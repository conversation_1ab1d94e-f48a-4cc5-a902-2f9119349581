program Prison2;

uses
  Vcl.Forms,
  Ufrm_articles in 'Ufrm_articles.pas' {frm_articles},
  ArabicCaptionUnit in 'ArabicCaptionUnit.pas',
  sharedfunctions in 'sharedfunctions.pas',
  ubetweenstores in 'ubetweenstores.pas' {betweenstores},
  ufrmMain in 'ufrmMain.pas' {frmMain},
  newempunit in 'newempunit.pas' {newemp},
  Usrchemp in 'Usrchemp.pas' {srchemp},
  Uptransform in 'Uptransform.pas' {ptransform},
  Uaddblace in 'Uaddblace.pas' {addblace},
  Udeparture in 'Udeparture.pas' {departure},
  Ualteremp in 'Ualteremp.pas' {alteremp},
  Uempreturn in 'Uempreturn.pas' {empreturn},
  Unit1 in 'Unit1.pas' {Form1},
  Uthecase in 'Uthecase.pas' {Form2};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TfrmMain, frmMain);
  Application.CreateForm(TForm2, Form2);
  Application.Run;
end.
