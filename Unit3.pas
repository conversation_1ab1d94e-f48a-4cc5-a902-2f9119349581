﻿unit Unit3;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, System.ImageList,
  Vcl.ImgList, Vcl.Menus, frxClass, frxDBSet, Data.DB, Data.Win.ADODB,
  dxGDIPlusClasses, Vcl.ExtCtrls, Vcl.Imaging.pngimage;

type
  TForm3 = class(TForm)
    Button1: TButton;
    Button2: TButton;
    Edit1: TEdit;
    ll: TLabel;
    Label1: TLabel;
    ADOConnection1: TADOConnection;
    ADOConnection2: TADOConnection;
    qbranch: TADOQuery;
    qinfo: TADOQuery;
    frxDBDataset2: TfrxDBDataset;
    qpermissions: TADOQuery;
    qstorenames: TADOQuery;
    frxDBDataset1: TfrxDBDataset;
    ImageList1: TImageList;
    qgen: TADOQuery;
    ComboBox1: TComboBox;
    q: TADOQuery;
    Image1: TImage;

    procedure Button1Click(Sender: TObject);
    procedure Button2Click(Sender: TObject);
    procedure ComboBox1KeyPress(Sender: TObject; var Key: Char);
    procedure ComboBox1KeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure Edit1KeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
    procedure LoadUsers;
    procedure checkinternet;
  public
    { Public declarations }
  end;

var
  Form3: TForm3;

implementation

uses
  ufrmMain, Unit4;

{$R *.dfm}

procedure TForm3.checkinternet;
begin
  // إجراء بسيط للتحقق من الإنترنت
  if via_internet then
  begin
    // يمكن إضافة فحص الإنترنت هنا لاحقاً
  end;
end;

procedure TForm3.LoadUsers;
var
  conn: TADOConnection;
  qUsers: TADOQuery;
begin
  ComboBox1.Items.Clear;
  try
    conn := TADOConnection.Create(nil);
    qUsers := TADOQuery.Create(nil);
    try
      conn.LoginPrompt := False;
      conn.ConnectionString := theconnectionstring;
      conn.Open();

      qUsers.Connection := conn;
      qUsers.SQL.Text := 'SELECT theusername FROM permissions ORDER BY theusername';
      qUsers.Open;

      while not qUsers.Eof do
      begin
        ComboBox1.Items.Add(qUsers.FieldByName('theusername').AsString);
        qUsers.Next;
      end;
      qUsers.Close;
    finally
      if Assigned(qUsers) then qUsers.Free;
      if Assigned(conn) then conn.Free;
    end;
  except
    on E: Exception do
      ShowMessage('خطأ في تحميل المستخدمين: ' + E.Message);
  end;
end;

procedure TForm3.FormCreate(Sender: TObject);
begin
  // استخدام نفس طريقة الاتصال المستخدمة في المشروع الأصلي
  try
    ADOConnection1.ConnectionString := 'FILE NAME=' + ExtractFilePath(Application.ExeName) + '\prison.udl';
    ADOConnection1.Open();
    theconnectionstring := ADOConnection1.ConnectionString;
  except
    on E: Exception do
    begin
      ShowMessage('خطأ في الاتصال بقاعدة البيانات: ' + E.Message + #13#10 + 'تأكد من وجود ملف prison.udl');
      Application.Terminate;
    end;
  end;

  via_internet := False;

  // تحميل المستخدمين
  LoadUsers;
end;

procedure TForm3.FormShow(Sender: TObject);
begin
  ComboBox1.SetFocus;
end;

procedure TForm3.Button1Click(Sender: TObject);
var
  qLogin: TADOQuery;
  conn: TADOConnection;
begin
  if Trim(ComboBox1.Text) = '' then
  begin
    ShowMessage('يرجى اختيار اسم المستخدم');
    ComboBox1.SetFocus;
    Exit;
  end;

  if Trim(Edit1.Text) = '' then
  begin
    ShowMessage('يرجى إدخال كلمة المرور');
    Edit1.SetFocus;
    Exit;
  end;

  checkinternet;

  try
    conn := TADOConnection.Create(nil);
    qLogin := TADOQuery.Create(nil);
    try
      conn.LoginPrompt := False;
      conn.ConnectionString := theconnectionstring;
      conn.Open();

      qLogin.Connection := conn;
      qLogin.SQL.Text := 'select * from permissions where theusername=' + QuotedStr(Trim(ComboBox1.Text)) +
                         ' and thepassword=' + QuotedStr(Trim(Edit1.Text));
      qLogin.Open;

      if qLogin.IsEmpty then
      begin
        ShowMessage('كلمة المرور غير صحيحة');
        Edit1.Text := '';
        Edit1.SetFocus;
      end
      else
      begin
        // حفظ بيانات المستخدم
        theusername := Trim(qLogin.FieldByName('theusername').AsString);
        theuserid := qLogin.FieldByName('perid').AsString;
        cangivepermissions := qLogin.FieldByName('cangivepermissions').AsBoolean;
        canprint := qLogin.FieldByName('canprint').AsBoolean;
        canaltercash := qLogin.FieldByName('canaltercash').AsBoolean;
        canalterfin := qLogin.FieldByName('canalterfin').AsBoolean;
        canregfin := qLogin.FieldByName('canregfin').AsBoolean;
        canpaycash := qLogin.FieldByName('canpaycash').AsBoolean;
        canreadfin := qLogin.FieldByName('canreadfin').AsBoolean;
        canread := qLogin.FieldByName('canread').AsBoolean;
        cansell := qLogin.FieldByName('cansell').AsBoolean;
        canrecievecash := qLogin.FieldByName('canrecievecash').AsBoolean;
        canaltersells := qLogin.FieldByName('canaltersells').AsBoolean;
        canreadcash := qLogin.FieldByName('canreadcash').AsBoolean;

        // إخفاء فورم تسجيل الدخول وإظهار لوحة التحكم
        Self.Hide;
        Form4.Show;
        Form4.WindowState := wsMaximized;
      end;
    finally
      if Assigned(qLogin) then qLogin.Free;
      if Assigned(conn) then conn.Free;
    end;
  except
    on E: Exception do
      ShowMessage('خطأ في الاتصال: ' + E.Message);
  end;
end;

procedure TForm3.Edit1KeyPress(Sender: TObject; var Key: Char);
begin
  if Key = Char(VK_RETURN) then
  begin
    Key := #0;
    Button1Click(Self);
  end;
end;

procedure TForm3.ComboBox1KeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  if (Key = VK_RETURN) then
  begin
    if ComboBox1.Text <> '' then
      Edit1.SetFocus
    else
      ShowMessage('حدد المستخدم');
  end;
end;

procedure TForm3.Button2Click(Sender: TObject);
begin
  Application.Terminate;
end;

procedure TForm3.ComboBox1KeyPress(Sender: TObject; var Key: Char);
begin
  // السماح بالكتابة في ComboBox
  // Key := #0; // تم حذف هذا السطر للسماح بالكتابة
end;

end.
