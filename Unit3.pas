unit Unit3;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, System.ImageList,
  Vcl.ImgList, Vcl.Menus, frxClass, frxDBSet, Data.DB, Data.Win.ADODB,
  dxGDIPlusClasses, Vcl.ExtCtrls, sharedfunctions;

type
  TForm3 = class(TForm)
    Button1: TButton;
    Button2: TButton;
    Edit1: TEdit;
    ll: TLabel;
    Label1: TLabel;
    ADOConnection1: TADOConnection;
    ADOConnection2: TADOConnection;
    qbranch: TADOQuery;
    qinfo: TADOQuery;
    frxDBDataset2: TfrxDBDataset;
    qpermissions: TADOQuery;
    qstorenames: TADOQuery;
    frxDBDataset1: TfrxDBDataset;
    ImageList1: TImageList;
    qgen: TADOQuery;
    ComboBox1: TComboBox;
    Image2: TImage;
    q: TADOQuery;

    procedure Button1Click(Sender: TObject);
    procedure Button2Click(Sender: TObject);
    procedure ComboBox1KeyPress(Sender: TObject; var Key: Char);
    procedure ComboBox1KeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure Edit1KeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  Form3: TForm3;

implementation

uses
  ufrmMain;

{$R *.dfm}

procedure TForm3.FormCreate(Sender: TObject);
var
  conn: TADOConnection;
begin
  // تحديد سلسلة الاتصال بقاعدة البيانات
  try
    ADOConnection1.ConnectionString := 'FILE NAME=' + ExtractFilePath(Application.ExeName) + '\prison.udl';
    ADOConnection1.Open();
    theconnectionstring := ADOConnection1.ConnectionString;
  except
    // في حالة عدم وجود ملف .udl، استخدم الاتصال المباشر
    theconnectionstring := 'Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;User ID="";Initial Catalog=prison_db;Data Source=.\SQLEXPRESS;Initial File Name="";Server SPN=""';
  end;

  via_internet := False;

  // ملء قائمة المستخدمين
  try
    conn := TADOConnection.Create(nil);
    try
      conn.LoginPrompt := False;
      conn.ConnectionString := theconnectionstring;
      conn.Open();
      q.Connection := conn;
      fillcombo(ComboBox1, q, 'permissions', 'theusername');
    finally
      q.Connection := nil;
      conn.Close;
      conn.Free;
    end;
  except
    on E: Exception do
      ShowMessage('خطأ في تحميل المستخدمين: ' + E.Message);
  end;
end;

procedure TForm3.FormShow(Sender: TObject);
begin
  ComboBox1.SetFocus;
end;

procedure TForm3.Button1Click(Sender: TObject);
var
  q: TADOQuery;
  conn: TADOConnection;
begin
  checkinternet;
  try
    try
      conn := TADOConnection.Create(nil);
      conn.LoginPrompt := False;
      q := TADOQuery.Create(nil);
      conn.ConnectionString := theconnectionstring;
      conn.Open();
      q.Connection := conn;
      q.SQL.Text := 'select * from permissions where theusername=' + QuotedStr(Trim(ComboBox1.Text)) + 
                    ' and thepassword=' + QuotedStr(Trim(Edit1.Text));
      q.Open;
      if q.IsEmpty then
      begin
        ShowMessage('كلمة المرور غير صحيحة');
        Edit1.Text := '';
        Edit1.SetFocus;
      end
      else
      begin
        // حفظ بيانات المستخدم
        theusername := Trim(q.FieldByName('theusername').AsString);
        theuserid := q.FieldByName('perid').AsString;
        cangivepermissions := q.FieldByName('cangivepermissions').AsBoolean;
        canprint := q.FieldByName('canprint').AsBoolean;
        canaltercash := q.FieldByName('canaltercash').AsBoolean;
        canalterfin := q.FieldByName('canalterfin').AsBoolean;
        canregfin := q.FieldByName('canregfin').AsBoolean;
        canpaycash := q.FieldByName('canpaycash').AsBoolean;
        canreadfin := q.FieldByName('canreadfin').AsBoolean;
        canread := q.FieldByName('canread').AsBoolean;
        cansell := q.FieldByName('cansell').AsBoolean;
        canrecievecash := q.FieldByName('canrecievecash').AsBoolean;
        canaltersells := q.FieldByName('canaltersells').AsBoolean;
        canreadcash := q.FieldByName('canreadcash').AsBoolean;

        // إخفاء فورم تسجيل الدخول وإظهار الفورم الرئيسي
        Self.Hide;
        frmMain.Show;
        frmMain.WindowState := wsMaximized;
        frmMain.Panel1.Visible := False;
        frmMain.dxNavBar1.Visible := True;
      end;
    except
      on E: Exception do
        ShowMessage('خطأ في الاتصال: ' + E.Message);
    end;
  finally
    if Assigned(q) then q.Free;
    if Assigned(conn) then conn.Free;
  end;
end;

procedure TForm3.Edit1KeyPress(Sender: TObject; var Key: Char);
begin
  if Key = Char(VK_RETURN) then
  begin
    Key := #0;
    Button1Click(Self);
  end;
end;

procedure TForm3.ComboBox1KeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  if (Key = VK_RETURN) then
  begin
    if ComboBox1.Text <> '' then
      Edit1.SetFocus
    else
      ShowMessage('حدد المستخدم');
  end;
end;

procedure TForm3.Button2Click(Sender: TObject);
begin
  Application.Terminate;
end;

procedure TForm3.ComboBox1KeyPress(Sender: TObject; var Key: Char);
begin
  // السماح بالكتابة في ComboBox
  // Key := #0; // تم حذف هذا السطر للسماح بالكتابة
end;

end.
