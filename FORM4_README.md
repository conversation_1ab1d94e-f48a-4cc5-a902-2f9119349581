# Form4 - لوحة التحكم العسكرية

## نظرة عامة 🎯

Form4 هو لوحة التحكم الرئيسية لنظام إدارة الحبس العسكري، مصمم بطابع عسكري عصري وألوان احترافية.

## المميزات الرئيسية ✨

### 🎨 التصميم العسكري العصري
- **ألوان عسكرية**: رمادي داكن، أسود، وألوان متدرجة احترافية
- **خط عصري**: Segoe UI للوضوح والأناقة
- **تخطيط متجاوب**: يتكيف مع جميع أحجام الشاشات
- **وضع ملء الشاشة**: WindowState = wsMaximized دائماً

### 📊 الرسم البياني التفاعلي
- **رسم بياني للعنابر**: يظهر عدد النزلاء في كل عنبر
- **ألوان متدرجة**: كل عنبر بلون مختلف
- **تحديث تلقائي**: البيانات تتحدث كل دقيقة
- **خلفية شفافة**: متناسقة مع التصميم العام

### 🔢 بطاقات الإحصائيات
1. **مجموع النزلاء** - لون أبيض
2. **العنابر المتاحة** - لون أخضر
3. **العنابر المشغولة** - لون أصفر
4. **التنبيهات** - لون أحمر

### 🎛️ لوحة الأزرار الجانبية
- **إدارة النزلاء** - إدارة شاملة للنزلاء
- **إدارة العنابر** - متابعة العنابر والسعة
- **التقارير** - تقارير مفصلة وإحصائيات
- **الأمن والحراسة** - نظام الأمن والمراقبة
- **إدارة الزيارات** - جدولة ومتابعة الزيارات
- **الخدمات الطبية** - الرعاية الصحية للنزلاء
- **إدارة الطعام** - نظام الوجبات والتغذية
- **الصيانة والترميم** - صيانة المرافق
- **إدارة الموظفين** - إدارة الكادر والمناوبات
- **الإعدادات** - إعدادات النظام العامة

### 🕒 شريط المعلومات العلوي
- **عنوان النظام**: نظام إدارة الحبس العسكري
- **التاريخ الحالي**: تحديث مستمر
- **الوقت الحالي**: تحديث كل ثانية
- **مكان للشعار**: صورة الوحدة أو الشعار الرسمي

## التقنيات المستخدمة 🛠️

### المكونات الرئيسية
- **TDBChart**: للرسم البياني التفاعلي
- **TBarSeries**: لعرض البيانات كأعمدة
- **TPanel**: للتخطيط والتنظيم
- **TTimer**: للتحديث التلقائي
- **TADOConnection**: للاتصال بقاعدة البيانات

### قاعدة البيانات
- **جدول cells**: معلومات العنابر
- **جدول prisoners**: بيانات النزلاء
- **جدول cells_statistics**: إحصائيات للرسم البياني
- **جدول alerts**: التنبيهات والإشعارات
- **فيو dashboard_stats**: إحصائيات سريعة

## الألوان المستخدمة 🎨

| العنصر | اللون | الكود |
|---------|--------|-------|
| خلفية رئيسية | رمادي داكن | RGB(43, 43, 43) |
| شريط علوي | أسود | RGB(25, 25, 25) |
| لوحة جانبية | رمادي متوسط | RGB(35, 35, 35) |
| منطقة المحتوى | رمادي فاتح | RGB(50, 50, 50) |
| بطاقات الإحصائيات | رمادي | RGB(66, 66, 66) |
| النصوص | أبيض | clWhite |
| النصوص الثانوية | فضي | clSilver |

## إعداد قاعدة البيانات 💾

### 1. تشغيل السكريبت
```sql
-- تشغيل create_dashboard_tables.sql
sqlcmd -S .\SQLEXPRESS -i create_dashboard_tables.sql
```

### 2. الجداول المطلوبة
- ✅ `cells` - العنابر
- ✅ `prisoners` - النزلاء  
- ✅ `cells_statistics` - إحصائيات العنابر
- ✅ `visits` - الزيارات
- ✅ `staff` - الموظفين
- ✅ `alerts` - التنبيهات

### 3. البيانات التجريبية
- 8 عنابر مختلفة الأنواع
- 5 نزلاء كعينة
- 5 موظفين
- 3 تنبيهات نشطة

## كيفية الاستخدام 🚀

### 1. تسجيل الدخول
```
1. تشغيل البرنامج
2. تسجيل الدخول من Form3
3. سيتم فتح Form4 تلقائياً
```

### 2. مراقبة الإحصائيات
- **البطاقات العلوية**: إحصائيات سريعة
- **الرسم البياني**: توزيع النزلاء على العنابر
- **التحديث التلقائي**: كل دقيقة

### 3. التنقل
- **الأزرار الجانبية**: للوصول للوظائف المختلفة
- **التصميم المتجاوب**: يتكيف مع حجم الشاشة

## الوظائف المتقدمة ⚡

### التحديث التلقائي
```pascal
procedure TForm4.tmrUpdateTimer(Sender: TObject);
begin
  UpdateDateTime;
  // تحديث الإحصائيات كل دقيقة
  if SecondOf(Now) = 0 then
  begin
    UpdateStatistics;
    LoadChartData;
  end;
end;
```

### تحميل بيانات الرسم البياني
```pascal
procedure TForm4.LoadChartData;
begin
  // قراءة البيانات من قاعدة البيانات
  // أو استخدام بيانات تجريبية
end;
```

### إعداد الألوان العسكرية
```pascal
procedure TForm4.SetupUI;
begin
  Self.Color := RGB(43, 43, 43);
  pnlHeader.Color := RGB(25, 25, 25);
  pnlRight.Color := RGB(35, 35, 35);
  pnlMain.Color := RGB(50, 50, 50);
end;
```

## التخصيص والتطوير 🔧

### إضافة وظائف جديدة
1. إضافة زر جديد في `pnlRight`
2. إنشاء إجراء `OnClick` للزر
3. ربط الزر بالفورم المطلوب

### تخصيص الألوان
- تعديل قيم RGB في `SetupUI`
- تغيير ألوان الرسم البياني في `SetupChart`

### إضافة إحصائيات جديدة
1. إضافة panel جديد في `pnlInfo`
2. إضافة استعلام في `UpdateStatistics`
3. ربط البيانات بالعرض

## الأمان والصلاحيات 🔒

### التحكم في الوصول
- يتم فتح Form4 فقط بعد تسجيل دخول ناجح
- الصلاحيات محفوظة من Form3
- يمكن إخفاء أزرار حسب صلاحيات المستخدم

### حماية البيانات
- جميع الاستعلامات محمية من SQL Injection
- استخدام QuotedStr للنصوص
- معالجة شاملة للأخطاء

## الاختبار والتشغيل ✅

### متطلبات التشغيل
- ✅ Delphi مع مكونات TeeChart
- ✅ SQL Server أو SQL Server Express
- ✅ ملف prison.udl صحيح
- ✅ قاعدة البيانات prison_db

### اختبار الوظائف
- [ ] فتح Form4 بعد تسجيل الدخول
- [ ] عرض الإحصائيات الصحيحة
- [ ] تحديث الرسم البياني
- [ ] عمل التحديث التلقائي
- [ ] استجابة الأزرار

## المشاكل الشائعة وحلولها 🔧

### مشكلة: الرسم البياني لا يظهر
**الحل**: تأكد من تثبيت مكونات TeeChart

### مشكلة: البيانات لا تظهر
**الحل**: 
1. تحقق من الاتصال بقاعدة البيانات
2. تشغيل create_dashboard_tables.sql
3. التأكد من وجود البيانات التجريبية

### مشكلة: الألوان لا تظهر صحيحة
**الحل**: تحقق من إعدادات النظام وألوان Windows

## الخطوات التالية 🎯

1. **ربط الأزرار**: ربط كل زر بالفورم المناسب
2. **إضافة المزيد من الإحصائيات**: رسوم بيانية إضافية
3. **تحسين الأداء**: تحسين استعلامات قاعدة البيانات
4. **إضافة التنبيهات**: نظام إشعارات متقدم
5. **التقارير**: إضافة تقارير مفصلة

---

**Form4 جاهز للاستخدام كلوحة تحكم عسكرية احترافية! 🎖️**
