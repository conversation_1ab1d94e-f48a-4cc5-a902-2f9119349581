program PrisonSimple;

uses
  Vcl.Forms,
  sharedfunctions in 'sharedfunctions.pas',
  ufrmMain in 'ufrmMain.pas' {frmMain},
  Unit3 in 'Unit3.pas' {Form3},
  Unit4 in 'Unit4.pas' {Form4};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TForm3, Form3);
  Application.CreateForm(TfrmMain, frmMain);
  Application.CreateForm(TForm4, Form4);
  Application.Run;
end.
