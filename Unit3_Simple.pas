unit Unit3_Simple;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, 
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, 
  Data.DB, Data.Win.ADODB, Vcl.ExtCtrls;

type
  TForm3 = class(TForm)
    Button1: TButton;
    Button2: TButton;
    Edit1: TEdit;
    ll: TLabel;
    Label1: TLabel;
    ADOConnection1: TADOConnection;
    ComboBox1: TComboBox;
    Image2: TImage;
    q: TADOQuery;

    procedure Button1Click(Sender: TObject);
    procedure Button2Click(Sender: TObject);
    procedure ComboBox1KeyPress(Sender: TObject; var Key: Char);
    procedure ComboBox1KeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure Edit1KeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
    procedure LoadUsers;
  public
    { Public declarations }
  end;

var
  Form3: TForm3;
  // متغيرات عامة لتسجيل الدخول
  theconnectionstring: string;
  via_internet: Boolean;
  theusername, theuserid: string;
  cangivepermissions, canprint, canaltercash, canalterfin, canregfin,
  canpaycash, canreadfin, canread, cansell, canrecievecash, canaltersells,
  canreadcash: Boolean;

implementation

{$R *.dfm}

procedure TForm3.FormCreate(Sender: TObject);
begin
  // تحديد سلسلة الاتصال بقاعدة البيانات
  theconnectionstring := 'Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;User ID="";Initial Catalog=prison_db;Data Source=.\SQLEXPRESS;Initial File Name="";Server SPN=""';
  via_internet := False;
  
  // تحميل المستخدمين
  LoadUsers;
end;

procedure TForm3.LoadUsers;
begin
  try
    ADOConnection1.LoginPrompt := False;
    ADOConnection1.ConnectionString := theconnectionstring;
    ADOConnection1.Open();
    
    q.Connection := ADOConnection1;
    q.SQL.Text := 'SELECT theusername FROM permissions ORDER BY theusername';
    q.Open;
    
    ComboBox1.Items.Clear;
    while not q.Eof do
    begin
      ComboBox1.Items.Add(q.FieldByName('theusername').AsString);
      q.Next;
    end;
    q.Close;
  except
    on E: Exception do
      ShowMessage('خطأ في تحميل المستخدمين: ' + E.Message);
  end;
end;

procedure TForm3.FormShow(Sender: TObject);
begin
  ComboBox1.SetFocus;
end;

procedure TForm3.Button1Click(Sender: TObject);
var
  qLogin: TADOQuery;
begin
  if Trim(ComboBox1.Text) = '' then
  begin
    ShowMessage('يرجى اختيار اسم المستخدم');
    ComboBox1.SetFocus;
    Exit;
  end;
  
  if Trim(Edit1.Text) = '' then
  begin
    ShowMessage('يرجى إدخال كلمة المرور');
    Edit1.SetFocus;
    Exit;
  end;

  try
    qLogin := TADOQuery.Create(nil);
    try
      qLogin.Connection := ADOConnection1;
      qLogin.SQL.Text := 'SELECT * FROM permissions WHERE theusername=' + QuotedStr(Trim(ComboBox1.Text)) + 
                         ' AND thepassword=' + QuotedStr(Trim(Edit1.Text));
      qLogin.Open;
      
      if qLogin.IsEmpty then
      begin
        ShowMessage('كلمة المرور غير صحيحة');
        Edit1.Text := '';
        Edit1.SetFocus;
      end
      else
      begin
        // حفظ بيانات المستخدم
        theusername := Trim(qLogin.FieldByName('theusername').AsString);
        theuserid := qLogin.FieldByName('perid').AsString;
        cangivepermissions := qLogin.FieldByName('cangivepermissions').AsBoolean;
        canprint := qLogin.FieldByName('canprint').AsBoolean;
        canaltercash := qLogin.FieldByName('canaltercash').AsBoolean;
        canalterfin := qLogin.FieldByName('canalterfin').AsBoolean;
        canregfin := qLogin.FieldByName('canregfin').AsBoolean;
        canpaycash := qLogin.FieldByName('canpaycash').AsBoolean;
        canreadfin := qLogin.FieldByName('canreadfin').AsBoolean;
        canread := qLogin.FieldByName('canread').AsBoolean;
        cansell := qLogin.FieldByName('cansell').AsBoolean;
        canrecievecash := qLogin.FieldByName('canrecievecash').AsBoolean;
        canaltersells := qLogin.FieldByName('canaltersells').AsBoolean;
        canreadcash := qLogin.FieldByName('canreadcash').AsBoolean;

        ShowMessage('مرحباً ' + theusername + '!' + #13#10 + 'تم تسجيل الدخول بنجاح');
        
        // يمكن هنا إظهار الفورم الرئيسي
        // Self.Hide;
        // frmMain.Show;
      end;
    finally
      qLogin.Free;
    end;
  except
    on E: Exception do
      ShowMessage('خطأ في الاتصال: ' + E.Message);
  end;
end;

procedure TForm3.Button2Click(Sender: TObject);
begin
  Application.Terminate;
end;

procedure TForm3.Edit1KeyPress(Sender: TObject; var Key: Char);
begin
  if Key = Char(VK_RETURN) then
  begin
    Key := #0;
    Button1Click(Self);
  end;
end;

procedure TForm3.ComboBox1KeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
begin
  if (Key = VK_RETURN) then
  begin
    if ComboBox1.Text <> '' then
      Edit1.SetFocus
    else
      ShowMessage('حدد المستخدم');
  end;
end;

procedure TForm3.ComboBox1KeyPress(Sender: TObject; var Key: Char);
begin
  // السماح بالكتابة في ComboBox
end;

end.
