-- سكريب<PERSON> إنشاء جدول المستخدمين لنظام الحبس
-- Create Login Table Script for Prison System

-- إنشاء قاعدة البيانات
CREATE DATABASE prison_db;
GO

USE prison_db;
GO

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الصلاحيات والمستخدمين
CREATE TABLE permissions (
    perid INT IDENTITY(1,1) PRIMARY KEY,
    theusername NVARCHAR(50) NOT NULL UNIQUE,
    thepassword NVARCHAR(50) NOT NULL,
    cangivepermissions BIT DEFAULT 0,
    canprint BIT DEFAULT 1,
    canaltercash BIT DEFAULT 0,
    canalterfin BIT DEFAULT 0,
    canregfin BIT DEFAULT 0,
    canpaycash BIT DEFAULT 0,
    canreadfin BIT DEFAULT 1,
    canread BIT DEFAULT 1,
    cansell BIT DEFAULT 0,
    canrecievecash BIT DEFAULT 0,
    canaltersells BIT DEFAULT 0,
    canreadcash BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE(),
    last_login DATETIME NULL
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الإعدادات العامة
CREATE TABLE generaltable (
    id INT IDENTITY(1,1) PRIMARY KEY,
    gencompanyname NVARCHAR(100) DEFAULT N'نظام إدارة الحبس',
    genmainaddress NVARCHAR(200) DEFAULT N'العنوان الرئيسي',
    gensubaddress NVARCHAR(200) DEFAULT N'العنوان الفرعي',
    gentelephone NVARCHAR(50) DEFAULT N'الهاتف',
    pagetailline1 NVARCHAR(100) DEFAULT N'السطر الأول',
    pagetailline2 NVARCHAR(100) DEFAULT N'السطر الثاني',
    check_tax DECIMAL(5,2) DEFAULT 0.00,
    subtract_hours INT DEFAULT 0
);

-- إدراج بيانات المستخدم الافتراضي (admin/admin)
INSERT INTO permissions (
    theusername, 
    thepassword, 
    cangivepermissions, 
    canprint, 
    canaltercash, 
    canalterfin, 
    canregfin, 
    canpaycash, 
    canreadfin, 
    canread, 
    cansell, 
    canrecievecash, 
    canaltersells, 
    canreadcash
) VALUES (
    N'admin', 
    N'admin', 
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
);

-- إدراج مستخدم عادي للاختبار (user/123)
INSERT INTO permissions (
    theusername, 
    thepassword, 
    cangivepermissions, 
    canprint, 
    canaltercash, 
    canalterfin, 
    canregfin, 
    canpaycash, 
    canreadfin, 
    canread, 
    cansell, 
    canrecievecash, 
    canaltersells, 
    canreadcash
) VALUES (
    N'user', 
    N'123', 
    0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1
);

-- إدراج الإعدادات العامة
INSERT INTO generaltable (
    gencompanyname,
    genmainaddress,
    gensubaddress,
    gentelephone,
    pagetailline1,
    pagetailline2,
    check_tax,
    subtract_hours
) VALUES (
    N'نظام إدارة الحبس',
    N'العنوان الرئيسي للمؤسسة',
    N'العنوان الفرعي',
    N'***********',
    N'جميع الحقوق محفوظة',
    N'نظام إدارة الحبس 2024',
    0.00,
    0
);

-- عرض البيانات المدرجة للتأكد
SELECT * FROM permissions;
SELECT * FROM generaltable;

PRINT N'تم إنشاء قاعدة البيانات وجداول المستخدمين بنجاح';
PRINT N'المستخدمون المتاحون:';
PRINT N'1. admin / admin (مدير النظام - جميع الصلاحيات)';
PRINT N'2. user / 123 (مستخدم عادي - صلاحيات محدودة)';
