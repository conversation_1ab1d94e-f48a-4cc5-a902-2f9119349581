object alteremp: Talteremp
  Left = 0
  Top = 0
  BiDiMode = bdRightToLeft
  Caption = #1578#1593#1583#1610#1604' '#1576#1610#1575#1606#1575#1578' '#1606#1586#1610#1604
  ClientHeight = 632
  ClientWidth = 611
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -16
  Font.Name = 'Arial'
  Font.Style = [fsBold]
  ParentBiDiMode = False
  Visible = True
  OnClose = FormClose
  TextHeight = 19
  object Label3: TLabel
    Left = 525
    Top = 10
    Width = 28
    Height = 19
    Caption = #1575#1604#1575#1587#1605
  end
  object Label2: TLabel
    Left = 521
    Top = 47
    Width = 29
    Height = 19
    Caption = #1575#1604#1601#1578#1585#1577
    Visible = False
  end
  object cmb_emp_name: TComboBox
    Left = 200
    Top = 7
    Width = 315
    Height = 27
    TabOrder = 0
    OnChange = cmb_emp_nameChange
    OnEnter = cmb_emp_nameEnter
  end
  object Button1: TButton
    Left = 138
    Top = 5
    Width = 60
    Height = 31
    Caption = #1576#1581#1579
    TabOrder = 1
    OnClick = Button1Click
  end
  object DBVertGridEh1: TDBVertGridEh
    Left = 8
    Top = 72
    Width = 595
    Height = 449
    AllowedSelections = []
    RowCategories.CategoryProps = <>
    PrintService.ColorSchema = pcsFullColorEh
    DataSource = DataSource1
    DrawGraphicData = True
    DrawMemoText = True
    TabOrder = 2
    LabelColWidth = 248
    Rows = <
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empname'
        ReadOnly = True
        RowLabel.Caption = #1575#1604#1575#1587#1605
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'ward'
        RowLabel.Caption = #1575#1604#1593#1606#1576#1585
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'room'
        RowLabel.Caption = #1575#1604#1581#1580#1585#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'notes'
        RowLabel.Caption = #1605#1604#1575#1581#1592#1575#1578
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'notes2'
        RowLabel.Caption = #1605#1604#1575#1581#1592#1575#1578' 2'
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empcharge'
        RowLabel.Caption = #1575#1604#1578#1607#1605#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empmother'
        RowLabel.Caption = #1575#1587#1605' '#1575#1604#1575#1605
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empgender'
        RowLabel.Caption = #1575#1604#1580#1606#1587
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empnationalty'
        RowLabel.Caption = #1575#1604#1580#1606#1587#1610#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empbirthdate'
        RowLabel.Caption = #1578#1575#1585#1610#1582' '#1575#1604#1605#1610#1604#1575#1583
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empbirthblace'
        RowLabel.Caption = #1605#1603#1575#1606' '#1575#1604#1605#1610#1604#1575#1583
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empidcard_no'
        RowLabel.Caption = #1585#1602#1605' '#1575#1604#1576#1591#1575#1602#1577' '#1575#1604#1588#1582#1589#1610#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empidcard_issuedate'
        RowLabel.Caption = #1578#1575#1585#1610#1582' '#1589#1583#1608#1585' '#1575#1604#1576#1591#1575#1602#1577' '#1575#1604#1588#1582#1589#1610#1577
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empidcard_issueblace'
        RowLabel.Caption = #1605#1603#1575#1606' '#1589#1583#1608#1585' '#1575#1604#1576#1591#1575#1602#1577' '#1575#1604#1588#1582#1589#1610#1577
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empnationalid'
        RowLabel.Caption = #1575#1604#1585#1602#1605' '#1575#1604#1608#1591#1606#1610
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empreg_id'
        RowLabel.Caption = #1585#1602#1605' '#1575#1604#1602#1610#1583
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empno'
        ReadOnly = True
        RowLabel.Caption = #1585#1602#1605' '#1575#1604#1606#1586#1610#1604
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empstartdate'
        RowLabel.Caption = #1578#1575#1585#1610#1582' '#1575#1604#1583#1582#1608#1604
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empsocialstate'
        RowLabel.Caption = #1575#1604#1581#1575#1604#1577' '#1575#1604#1575#1580#1578#1605#1575#1593#1610#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empdegree'
        RowLabel.Caption = #1575#1604#1583#1585#1580#1577
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empdegreedate'
        RowLabel.Caption = #1578#1575#1585#1610#1582' '#1575#1604#1581#1589#1608#1604' '#1593#1604#1609' '#1575#1604#1583#1585#1580#1577
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empbank'
        RowLabel.Caption = #1575#1604#1605#1589#1585#1601
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empbankno'
        RowLabel.Caption = #1585#1602#1605' '#1575#1604#1581#1587#1575#1576' '#1575#1604#1605#1589#1585#1601#1610
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empdepartment'
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'emppremium_value'
        RowLabel.Caption = #1602#1610#1605#1577' '#1575#1604#1593#1604#1575#1608#1577' '#1575#1604#1587#1606#1608#1610#1577
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empqualifier'
        RowLabel.Caption = #1575#1604#1605#1572#1607#1604
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'emppassportid'
        RowLabel.Caption = #1585#1602#1605' '#1580#1608#1575#1586' '#1575#1604#1587#1601#1585
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empphone'
        RowLabel.Caption = #1585#1602#1605' '#1575#1604#1607#1575#1578#1601
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empdivision'
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empentryno'
        RowLabel.Caption = #1585#1602#1605' '#1575#1604#1602#1610#1583
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'deptname'
        ReadOnly = True
        RowLabel.Caption = #1575#1604#1575#1583#1575#1585#1577' '#1575#1604#1578#1575#1576#1593' '#1604#1607#1575
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'subdeptname'
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empemail'
        RowLabel.Caption = #1575#1604#1576#1585#1610#1583' '#1575#1604#1575#1604#1603#1578#1585#1608#1606#1610
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'passportdate'
        RowLabel.Caption = #1578#1575#1585#1610#1582' '#1589#1583#1608#1585' '#1580#1608#1575#1586' '#1575#1604#1587#1601#1585
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'passportplace'
        RowLabel.Caption = #1605#1603#1575#1606' '#1589#1583#1608#1585' '#1580#1608#1575#1586' '#1575#1604#1587#1601#1585
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'idcarddate'
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'idplace'
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'emppremiums'
        RowLabel.Caption = #1575#1604#1593#1604#1575#1608#1575#1578
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'vacation_outstanding'
        RowLabel.Caption = #1585#1589#1610#1583' '#1575#1604#1575#1580#1575#1586#1577
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'attendance_no'
        RowLabel.Caption = #1585#1602#1605' '#1575#1604#1605#1608#1592#1601' '#1601#1610' '#1575#1604#1577' '#1575#1604#1576#1589#1605#1577'('#1604#1610#1587' '#1585#1602#1605' '#1575#1604#1576#1589#1605#1577')'
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'empstate'
        ReadOnly = True
        RowLabel.Caption = #1575#1604#1581#1575#1604#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'workperiod'
        RowLabel.Caption = #1608#1602#1578' '#1575#1604#1583#1608#1575#1605
        Visible = False
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'childs'
        RowLabel.Caption = #1593#1583#1583' '#1575#1604#1575#1576#1606#1575#1569
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'brothers'
        RowLabel.Caption = #1593#1583#1583' '#1575#1601#1585#1575#1583' '#1575#1604#1575#1587#1585#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'address'
        RowLabel.Caption = #1575#1604#1593#1606#1608#1575#1606
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'relitive'
        RowLabel.Caption = #1575#1602#1585#1576' '#1575#1604#1575#1602#1575#1585#1576
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'relitiveaddress'
        RowLabel.Caption = #1593#1606#1608#1575#1606' '#1575#1602#1585#1576' '#1575#1604#1575#1602#1575#1585#1576
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'relitivephone'
        RowLabel.Caption = #1607#1575#1578#1601' '#1575#1602#1585#1576' '#1575#1604#1575#1602#1575#1585#1576
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'health'
        RowLabel.Caption = #1575#1604#1581#1575#1604#1577' '#1575#1604#1589#1581#1610#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'qualification'
        RowLabel.Caption = #1575#1604#1605#1572#1607#1604
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'politicalstatus'
        RowLabel.Caption = #1575#1604#1581#1575#1604#1577' '#1575#1604#1587#1610#1575#1587#1610#1577
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'workinpridson'
        RowLabel.Caption = #1575#1604#1593#1605#1604' '#1575#1604#1605#1603#1604#1601' '#1576#1607' '#1576#1575#1604#1587#1580#1606
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'workplace'
        RowLabel.Caption = #1580#1607#1577' '#1575#1604#1593#1605#1604
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'paneltycountinprison'
        RowLabel.Caption = #1593#1583#1583' '#1575#1604#1593#1602#1608#1576#1575#1578' '#1583#1575#1582#1604' '#1575#1604#1587#1580#1606
      end
      item
        DynProps = <>
        EditButtons = <>
        FieldName = 'stateofprisonerinprison'
        RowLabel.Caption = #1587#1610#1585#1577' '#1575#1604#1606#1586#1610#1604' '#1583#1575#1582#1604' '#1575#1604#1587#1580#1606
      end>
  end
  object DBNavigator1: TDBNavigator
    Left = 163
    Top = 538
    Width = 294
    Height = 60
    DataSource = DataSource1
    VisibleButtons = [nbFirst, nbPrior, nbNext, nbLast, nbPost, nbRefresh]
    TabOrder = 3
  end
  object Button3: TButton
    Left = 472
    Top = 536
    Width = 121
    Height = 52
    Caption = #1578#1593#1583#1610#1604
    TabOrder = 4
    OnClick = Button3Click
  end
  object Panel1: TPanel
    Left = 135
    Top = 173
    Width = 391
    Height = 239
    TabOrder = 5
    Visible = False
    OnExit = Panel1Exit
    object Label1: TLabel
      Left = 311
      Top = 53
      Width = 28
      Height = 19
      Caption = #1575#1604#1602#1583#1610#1605
    end
    object Label4: TLabel
      Left = 310
      Top = 112
      Width = 30
      Height = 19
      Caption = #1575#1604#1580#1583#1610#1583
    end
    object Label5: TLabel
      Left = 166
      Top = 11
      Width = 30
      Height = 19
      Caption = '*****'
    end
    object Button2: TButton
      Left = 16
      Top = 8
      Width = 25
      Height = 25
      Caption = 'x'
      TabOrder = 0
      OnClick = Button2Click
    end
    object Button4: TButton
      Left = 157
      Top = 189
      Width = 100
      Height = 37
      Caption = #1578#1593#1583#1610#1604
      TabOrder = 1
      OnClick = Button4Click
    end
    object edtold: TEdit
      Left = 60
      Top = 75
      Width = 283
      Height = 27
      ReadOnly = True
      TabOrder = 2
    end
    object edtnew: TEdit
      Left = 62
      Top = 131
      Width = 280
      Height = 27
      TabOrder = 3
    end
  end
  object ComboBox2: TComboBox
    Left = 370
    Top = 39
    Width = 145
    Height = 27
    TabOrder = 6
    Visible = False
    OnKeyPress = ComboBox2KeyPress
    Items.Strings = (
      #1575#1604#1601#1578#1585#1577' '#1575#1604#1589#1576#1575#1581#1610#1577
      #1575#1604#1601#1578#1585#1577' '#1575#1604#1605#1587#1575#1574#1610#1577
      #1575#1604#1601#1578#1585#1577' '#1575#1604#1604#1610#1604#1610#1577)
  end
  object Button5: TButton
    Left = 6
    Top = 7
    Width = 66
    Height = 25
    Caption = #1582#1585#1608#1580
    TabOrder = 7
    OnClick = Button5Click
  end
  object Button6: TButton
    Left = 473
    Top = 597
    Width = 118
    Height = 25
    Caption = #1581#1584#1601' '#1575#1604#1606#1586#1610#1604
    TabOrder = 8
    OnClick = Button6Click
  end
  object Button7: TButton
    Left = 18
    Top = 527
    Width = 123
    Height = 40
    Caption = #1578#1593#1583#1610#1604' '#1575#1587#1605' '#1575#1604#1606#1586#1610#1604
    TabOrder = 9
    OnClick = Button7Click
  end
  object Button8: TButton
    Left = 18
    Top = 576
    Width = 123
    Height = 38
    Caption = #1578#1593#1583#1610#1604' '#1585#1602#1605' '#1575#1604#1606#1586#1610#1604
    TabOrder = 10
    OnClick = Button8Click
  end
  object DataSource1: TDataSource
    DataSet = qemp
    Left = 292
    Top = 96
  end
  object qgen: TADOQuery
    Connection = frmMain.ADOConnection1
    Parameters = <>
    Left = 41
    Top = 52
  end
  object q1: TADOQuery
    Connection = frmMain.ADOConnection1
    Parameters = <>
    Left = 40
    Top = 136
  end
  object qemp: TADOQuery
    Connection = frmMain.ADOConnection1
    Parameters = <>
    Left = 40
    Top = 208
  end
  object PopupMenu1: TPopupMenu
    Left = 180
    Top = 156
    object N1: TMenuItem
      Caption = #1581#1584#1601' '#1575#1604#1606#1586#1610#1604
    end
    object N2: TMenuItem
      Caption = #1578#1593#1583#1610#1604' '#1575#1587#1605' '#1575#1604#1606#1586#1610#1604
    end
    object N3: TMenuItem
      Caption = #1578#1593#1583#1610#1604' '#1585#1602#1605' '#1575#1604#1606#1586#1610#1604
    end
  end
end
