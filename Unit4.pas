unit Unit4;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, 
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.Buttons, Data.DB, Data.Win.ADODB, VclTee.TeeGDIPlus, VCLTee.TeEngine, 
  VCLTee.Series, VCLTee.TeeProcs, VCLTee.Chart, VCLTee.DBChart, System.ImageList,
  Vcl.ImgList, Vcl.ComCtrls;

type
  TForm4 = class(TForm)
    // Header Panel - للشعار والتاريخ
    pnlHeader: TPanel;
    lblTitle: TLabel;
    lblDate: TLabel;
    lblTime: TLabel;
    imgLogo: TImage;
    
    // Right Panel - للأزرار
    pnlRight: TPanel;
    btnPrisoners: TButton;
    btnCells: TButton;
    btnReports: TButton;
    btnSecurity: TButton;
    btnVisitors: TButton;
    btnMedical: TButton;
    btnFood: TButton;
    btnMaintenance: TButton;
    btnStaff: TButton;
    btnSettings: TButton;
    
    // Main Panel - للمحتوى الرئيسي
    pnlMain: TPanel;
    
    // Chart Panel - للرسم البياني
    pnlChart: TPanel;
    chartCells: TDBChart;
    seriesCells: TBarSeries;
    
    // Info Panels - لعرض المعلومات
    pnlInfo: TPanel;
    pnlTotalPrisoners: TPanel;
    lblTotalPrisoners: TLabel;
    lblTotalPrisonersValue: TLabel;
    
    pnlAvailableCells: TPanel;
    lblAvailableCells: TLabel;
    lblAvailableCellsValue: TLabel;
    
    pnlOccupiedCells: TPanel;
    lblOccupiedCells: TLabel;
    lblOccupiedCellsValue: TLabel;
    
    pnlAlerts: TPanel;
    lblAlerts: TLabel;
    lblAlertsValue: TLabel;
    
    // Database Components
    ADOConnection1: TADOConnection;
    qCells: TADOQuery;
    qStats: TADOQuery;
    
    // Timer for updates
    tmrUpdate: TTimer;
    
    // ImageList for buttons
    ImageList1: TImageList;

    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormResize(Sender: TObject);
    procedure tmrUpdateTimer(Sender: TObject);
    procedure btnPrisonersClick(Sender: TObject);
    procedure btnCellsClick(Sender: TObject);
    procedure btnReportsClick(Sender: TObject);
    procedure btnSecurityClick(Sender: TObject);
    procedure btnVisitorsClick(Sender: TObject);
    procedure btnMedicalClick(Sender: TObject);
    procedure btnFoodClick(Sender: TObject);
    procedure btnMaintenanceClick(Sender: TObject);
    procedure btnStaffClick(Sender: TObject);
    procedure btnSettingsClick(Sender: TObject);
    
  private
    { Private declarations }
    procedure SetupUI;
    procedure LoadChartData;
    procedure UpdateStatistics;
    procedure UpdateDateTime;
    procedure SetupChart;
    
  public
    { Public declarations }
  end;

var
  Form4: TForm4;
  theconnectionstring: string;

implementation

{$R *.dfm}

procedure TForm4.FormCreate(Sender: TObject);
begin
  // إعداد الاتصال بقاعدة البيانات
  theconnectionstring := 'Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;User ID="";Initial Catalog=prison_db;Data Source=.\SQLEXPRESS;Initial File Name="";Server SPN=""';

  try
    ADOConnection1.ConnectionString := theconnectionstring;
    ADOConnection1.Open();
  except
    on E: Exception do
      ShowMessage('خطأ في الاتصال بقاعدة البيانات: ' + E.Message);
  end;

  // إعداد الواجهة
  SetupUI;
  SetupChart;

  // بدء التحديث التلقائي
  tmrUpdate.Enabled := True;
end;

procedure TForm4.FormShow(Sender: TObject);
begin
  // تحديث البيانات عند إظهار الفورم
  LoadChartData;
  UpdateStatistics;
  UpdateDateTime;
end;

procedure TForm4.FormResize(Sender: TObject);
begin
  // إعادة ترتيب العناصر عند تغيير حجم النافذة
  if Assigned(pnlRight) then
    pnlRight.Width := 250;
    
  if Assigned(pnlHeader) then
    pnlHeader.Height := 80;
end;

procedure TForm4.SetupUI;
begin
  // إعداد الألوان والخطوط العسكرية
  Self.Color := RGB(43, 43, 43); // لون رمادي داكن عسكري
  
  // إعداد Header Panel
  pnlHeader.Color := RGB(25, 25, 25);
  pnlHeader.Height := 80;
  pnlHeader.Align := alTop;
  
  lblTitle.Font.Size := 18;
  lblTitle.Font.Style := [fsBold];
  lblTitle.Font.Color := clWhite;
  lblTitle.Caption := 'نظام إدارة الحبس العسكري - لوحة التحكم';
  
  // إعداد Right Panel
  pnlRight.Color := RGB(35, 35, 35);
  pnlRight.Width := 250;
  pnlRight.Align := alRight;
  
  // إعداد Main Panel
  pnlMain.Color := RGB(50, 50, 50);
  pnlMain.Align := alClient;
end;

procedure TForm4.SetupChart;
begin
  // إعداد الرسم البياني
  chartCells.Color := RGB(50, 50, 50);
  chartCells.BackColor := RGB(50, 50, 50);
  chartCells.Title.Text.Clear;
  chartCells.Title.Text.Add('إحصائيات العنابر');
  chartCells.Title.Font.Color := clWhite;
  chartCells.Title.Font.Size := 14;
  chartCells.Title.Font.Style := [fsBold];
  
  // إعداد السلسلة
  seriesCells.Title := 'عدد النزلاء';
  seriesCells.Color := RGB(0, 120, 215); // أزرق عسكري
end;

procedure TForm4.LoadChartData;
begin
  try
    qCells.Connection := ADOConnection1;
    qCells.SQL.Text := 'SELECT cell_name, prisoner_count FROM cells_statistics ORDER BY cell_name';
    qCells.Open;
    
    seriesCells.Clear;
    while not qCells.Eof do
    begin
      seriesCells.Add(qCells.FieldByName('prisoner_count').AsInteger, 
                      qCells.FieldByName('cell_name').AsString);
      qCells.Next;
    end;
    qCells.Close;
  except
    on E: Exception do
    begin
      // في حالة عدم وجود الجدول، إنشاء بيانات تجريبية
      seriesCells.Clear;
      seriesCells.Add(25, 'العنبر الأول');
      seriesCells.Add(18, 'العنبر الثاني');
      seriesCells.Add(32, 'العنبر الثالث');
      seriesCells.Add(15, 'العنبر الرابع');
      seriesCells.Add(28, 'العنبر الخامس');
    end;
  end;
end;

procedure TForm4.UpdateStatistics;
begin
  try
    qStats.Connection := ADOConnection1;
    qStats.SQL.Text := 'SELECT COUNT(*) as total_prisoners FROM prisoners WHERE status = ''active''';
    qStats.Open;
    lblTotalPrisonersValue.Caption := qStats.FieldByName('total_prisoners').AsString;
    qStats.Close;
    
    qStats.SQL.Text := 'SELECT COUNT(*) as available_cells FROM cells WHERE status = ''available''';
    qStats.Open;
    lblAvailableCellsValue.Caption := qStats.FieldByName('available_cells').AsString;
    qStats.Close;
    
    qStats.SQL.Text := 'SELECT COUNT(*) as occupied_cells FROM cells WHERE status = ''occupied''';
    qStats.Open;
    lblOccupiedCellsValue.Caption := qStats.FieldByName('occupied_cells').AsString;
    qStats.Close;
  except
    // بيانات تجريبية في حالة عدم وجود الجداول
    lblTotalPrisonersValue.Caption := '118';
    lblAvailableCellsValue.Caption := '12';
    lblOccupiedCellsValue.Caption := '23';
    lblAlertsValue.Caption := '3';
  end;
end;

procedure TForm4.UpdateDateTime;
begin
  lblDate.Caption := FormatDateTime('yyyy/mm/dd', Now);
  lblTime.Caption := FormatDateTime('hh:nn:ss', Now);
end;

procedure TForm4.tmrUpdateTimer(Sender: TObject);
var
  CurrentTime: TDateTime;
begin
  CurrentTime := Now;
  UpdateDateTime;
  // تحديث الإحصائيات كل دقيقة
  if (Trunc(CurrentTime * 24 * 60) mod 1) = 0 then
  begin
    UpdateStatistics;
    LoadChartData;
  end;
end;

// إجراءات الأزرار - ستتم إضافة الوظائف لاحقاً
procedure TForm4.btnPrisonersClick(Sender: TObject);
begin
  // سيتم ربطها بفورم إدارة النزلاء
end;

procedure TForm4.btnCellsClick(Sender: TObject);
begin
  // سيتم ربطها بفورم إدارة العنابر
end;

procedure TForm4.btnReportsClick(Sender: TObject);
begin
  // سيتم ربطها بفورم التقارير
end;

procedure TForm4.btnSecurityClick(Sender: TObject);
begin
  // سيتم ربطها بفورم الأمن
end;

procedure TForm4.btnVisitorsClick(Sender: TObject);
begin
  // سيتم ربطها بفورم الزيارات
end;

procedure TForm4.btnMedicalClick(Sender: TObject);
begin
  // سيتم ربطها بفورم الطبي
end;

procedure TForm4.btnFoodClick(Sender: TObject);
begin
  // سيتم ربطها بفورم الطعام
end;

procedure TForm4.btnMaintenanceClick(Sender: TObject);
begin
  // سيتم ربطها بفورم الصيانة
end;

procedure TForm4.btnStaffClick(Sender: TObject);
begin
  // سيتم ربطها بفورم الموظفين
end;

procedure TForm4.btnSettingsClick(Sender: TObject);
begin
  // سيتم ربطها بفورم الإعدادات
end;

end.
