program Prison;

uses
  Vcl.Forms,
  Ufrm_articles in 'Ufrm_articles.pas' {frm_articles},
  ArabicCaptionUnit in 'ArabicCaptionUnit.pas',
  sharedfunctions in 'sharedfunctions.pas',
  ubetweenstores in 'ubetweenstores.pas' {betweenstores},
  ufrmMain in 'ufrmMain.pas' {frmMain},
  newempunit in 'newempunit.pas' {newemp},
  Usrchemp in 'Usrchemp.pas' {srchemp},
  Uptransform in 'Uptransform.pas' {ptransform},
  Uaddblace in 'Uaddblace.pas' {addblace},
  Udeparture in 'Udeparture.pas' {departure};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TfrmMain, frmMain);
  Application.Run;
end.
