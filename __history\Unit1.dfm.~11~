object Form1: TForm1
  Left = 0
  Top = 0
  BiDiMode = bdRightToLeft
  Caption = 'Form1'
  ClientHeight = 625
  ClientWidth = 1150
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  ParentBiDiMode = False
  OnCreate = FormCreate
  TextHeight = 13
  object Label31: TLabel
    Left = 830
    Top = 14
    Width = 14
    Height = 13
    Caption = #1605#1606
  end
  object Label32: TLabel
    Left = 585
    Top = 9
    Width = 17
    Height = 13
    Caption = #1575#1604#1609
  end
  object Label1: TLabel
    Left = 1048
    Top = 12
    Width = 55
    Height = 13
    Caption = #1575#1604#1576#1581#1579' '#1576#1578#1575#1585#1610#1582
  end
  object DBGridEh3: TDBGridEh
    Left = 10
    Top = 39
    Width = 1132
    Height = 467
    DataSource = DataSource1
    DynProps = <>
    ReadOnly = True
    TabOrder = 0
    Columns = <
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_operation'
        Footers = <>
        Width = 120
      end
      item
        CellButtons = <>
        DisplayFormat = 'dd/mm/yyyy'
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_date'
        Footers = <>
        Width = 127
      end
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'empname'
        Footers = <>
        Width = 225
      end
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_log'
        Footers = <>
        Width = 212
      end
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'theusername'
        Footers = <>
        Width = 177
      end
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_regdate'
        Footers = <>
        Width = 285
      end>
    object RowDetailData: TRowDetailPanelControlEh
    end
  end
  object DateTimePicker5: TDateTimePicker
    Left = 696
    Top = 8
    Width = 121
    Height = 27
    Date = 45262.000000000000000000
    Time = 0.962771377322496800
    TabOrder = 1
  end
  object DateTimePicker6: TDateTimePicker
    Left = 461
    Top = 7
    Width = 122
    Height = 27
    Date = 45262.000000000000000000
    Time = 0.962837314822536400
    TabOrder = 2
  end
  object Button15: TButton
    Left = 269
    Top = 7
    Width = 75
    Height = 25
    Caption = #1576#1581#1579
    TabOrder = 3
    OnClick = Button15Click
  end
  object DBGridEh1: TDBGridEh
    Left = 10
    Top = 511
    Width = 1132
    Height = 120
    DataSource = DataSource2
    DynProps = <>
    ReadOnly = True
    TabOrder = 4
    Columns = <
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_operation'
        Footers = <>
        Width = 120
      end
      item
        CellButtons = <>
        DisplayFormat = 'dd/mm/yyyy'
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_date'
        Footers = <>
        Width = 127
      end
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_log'
        Footers = <>
        Width = 212
      end
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'theusername'
        Footers = <>
        Width = 177
      end
      item
        CellButtons = <>
        DynProps = <>
        EditButtons = <>
        FieldName = 'el_regdate'
        Footers = <>
        Width = 285
      end>
    object RowDetailData: TRowDetailPanelControlEh
    end
  end
  object ComboBox1: TComboBox
    Left = 897
    Top = 10
    Width = 145
    Height = 21
    TabOrder = 5
    Text = #1575#1604#1581#1583#1579
    OnKeyPress = ComboBox1KeyPress
    Items.Strings = (
      #1575#1604#1581#1583#1579
      #1575#1604#1578#1587#1580#1610#1604)
  end
  object qloghistory: TADOQuery
    Connection = frmMain.ADOConnection1
    CursorType = ctStatic
    Parameters = <>
    SQL.Strings = (
      'select theusername,empname'
      ',el_id,el_date,el_log,el_empid,el_regdate,el_operation'
      ' from emplog join permissions on el_userid=perid'
      'join emptable on empid=el_empid')
    Left = 499
    Top = 332
  end
  object DataSource1: TDataSource
    DataSet = qloghistory
    Left = 702
    Top = 380
  end
  object DataSource2: TDataSource
    DataSet = qdelete
    Left = 165
    Top = 440
  end
  object qdelete: TADOQuery
    Connection = frmMain.ADOConnection1
    Parameters = <>
    Left = 204
    Top = 197
  end
end
