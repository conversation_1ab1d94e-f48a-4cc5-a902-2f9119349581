unit Uempreturn;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ComCtrls, Data.DB,
  Data.Win.ADODB;

type
  Tempreturn = class(TForm)
    Label3: TLabel;
    DateTimePicker1: TDateTimePicker;
    edtempid: TEdit;
    Button1: TButton;
    cmbroomto: TComboBox;
    cmbstoreto: TComboBox;
    Label23: TLabel;
    qgen: TADOQuery;
    procedure cmbstoretoEnter(Sender: TObject);
    procedure cmbstoretoKeyPress(Sender: TObject; var Key: Char);
    procedure cmbroomtoKeyPress(Sender: TObject; var Key: Char);
    procedure cmbroomtoEnter(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  empreturn: Tempreturn;

implementation

uses
  ufrmMain;

{$R *.dfm}

procedure Tempreturn.cmbroomtoEnter(Sender: TObject);
begin
  cmbroomto.Items.Clear;
  if cmbstoreto.Text <> '' then
  begin

      qgen.SQL.Text:='select distinct room from emptable where room is not null and ward='+QuotedStr(cmbstorefrom.Text) ;


    qgen.Open;
    if not qgen.IsEmpty then
    begin
      while not qgen.Eof do
      begin
        cmbroomto.Items.Add(qgen['room']);
        qgen.Next;
      end;
    end;
  end;
end;

procedure Tempreturn.cmbroomtoKeyPress(Sender: TObject; var Key: Char);
begin
key:=#0;
end;

procedure Tempreturn.cmbstoretoEnter(Sender: TObject);
var
  q: TADOQuery;

begin
  cmbstoreto.Items.Clear;
  try
    q := TADOQuery.Create(nil);
    q.Connection := frmMain.ADOConnection1;
    q.SQL.Text :=
      'select distinct ward from emptable where ward is not null  order by ward';
    q.Open;
    if not q.IsEmpty then
    begin
      while not q.Eof do
      begin

        cmbstoreto.Items.Add(q['ward']);
        q.Next;
      end;
    end;

  finally
    q.Free;
  end;
end;

procedure Tempreturn.cmbstoretoKeyPress(Sender: TObject; var Key: Char);
begin
key:=#0;
end;

end.
